import React from 'react';
import { motion } from 'framer-motion';
import { Users, Mail, Phone, MapPin, Calendar, Trophy, Zap, Star, Award } from 'lucide-react';

const About = () => {
  const organizers = [
    {
      id: 1,
      name: 'Person1',
      role: 'Post1',
      hostel: 'AH1',
      email: '<EMAIL>',
      phone: '+91-9876543210',
      image: '👨‍💼'
    }
  ];

  const festivalStats = [
    { label: 'Years of Legacy', value: '15+', icon: Calendar },
    { label: 'Participating Hostels', value: '18', icon: Users },
    { label: 'Total Events', value: '30+', icon: Trophy },
    { label: 'Expected Participants', value: '3000+', icon: Star },
    { label: 'Days of Action', value: '3', icon: Zap }
  ];

  const timeline = [
    {
      date: 'Day 1',
      title: 'Opening Ceremony',
      description: 'Grand inauguration with cultural performances and hostel parade'
    },
    {
      date: 'Day 2',
      title: 'Main Events',
      description: 'Sports, cultural, and technical competitions across all categories'
    },
    {
      date: 'Day 3',
      title: 'Grand Finale & Closing',
      description: 'Final matches, cultural night, prize distribution and closing ceremony'
    }
  ];

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            About Zephyr 2025
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            BITS Goa's premier inter-hostel festival bringing together 18 hostels in a celebration of
            sports, culture, and technical excellence. A tradition of competition, camaraderie, and unforgettable memories.
          </p>
        </motion.div>

        {/* Festival Overview */}
        <motion.div
          className="mb-16 bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 md:p-12"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">The Spirit of Zephyr</h2>
              <div className="space-y-4 text-gray-300">
                <p>
                  Zephyr represents the pinnacle of inter-hostel competition at BITS Goa, where 18 hostels
                  compete across 30+ events spanning sports, cultural arts, and technical innovation.
                  More than just a festival, it's a celebration of talent, teamwork, and the indomitable spirit of BITSians.
                </p>
                <p>
                  From the thunderous cheers at football matches to the mesmerizing performances at cultural nights, 
                  from intense coding competitions to innovative hackathons, Zephyr showcases the diverse talents 
                  of our vibrant campus community.
                </p>
                <p>
                  Every year, Zephyr brings together students from different backgrounds, fostering friendships 
                  that last a lifetime while maintaining the healthy competitive spirit that drives excellence.
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              {festivalStats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="p-6 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-2xl backdrop-blur-sm border border-cyan-500/20 text-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  whileHover={{ scale: 1.05 }}
                >
                  <stat.icon className="w-8 h-8 mx-auto mb-3 text-cyan-400" />
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Timeline */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-center mb-12 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Festival Timeline
          </h2>
          
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-cyan-500 to-fuchsia-500 rounded-full"></div>
            
            <div className="space-y-12">
              {timeline.map((event, index) => (
                <motion.div
                  key={index}
                  className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="p-6 bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl">
                      <h3 className="text-xl font-bold text-white mb-2">{event.title}</h3>
                      <p className="text-cyan-400 font-medium mb-2">{event.date}</p>
                      <p className="text-gray-300">{event.description}</p>
                    </div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="w-4 h-4 bg-gradient-to-r from-cyan-500 to-fuchsia-500 rounded-full border-4 border-white/20"></div>
                  </div>
                  
                  <div className="w-1/2"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Organizing Committee */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h2 className="text-3xl font-bold text-center mb-12 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Meet the Organizing Committee
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {organizers.map((organizer, index) => (
              <motion.div
                key={organizer.id}
                className="group relative bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:border-white/20 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                whileHover={{ scale: 1.02, y: -5 }}
              >
                <div className="text-center mb-6">
                  <div className="text-6xl mb-4">{organizer.image}</div>
                  <h3 className="text-xl font-bold text-white mb-1">{organizer.name}</h3>
                  <p className="text-cyan-400 font-medium mb-1">{organizer.role}</p>
                  <p className="text-gray-400 text-sm">{organizer.hostel}</p>
                </div>

                <div className="space-y-3">
                  <a
                    href={`mailto:${organizer.email}`}
                    className="flex items-center space-x-3 text-gray-300 hover:text-cyan-400 transition-colors"
                  >
                    <Mail className="w-4 h-4" />
                    <span className="text-sm">{organizer.email}</span>
                  </a>

                  <a
                    href={`tel:${organizer.phone}`}
                    className="flex items-center space-x-3 text-gray-300 hover:text-cyan-400 transition-colors"
                  >
                    <Phone className="w-4 h-4" />
                    <span className="text-sm">{organizer.phone}</span>
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 md:p-12"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <h2 className="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Get in Touch
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Contact</h3>
              <p className="text-gray-300">Ayush Singh</p>
              <p className="text-gray-300"><EMAIL></p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-fuchsia-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Call Us</h3>
              <p className="text-gray-300">+91-9876543210</p>
              <p className="text-gray-300">+91-9876543211</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Visit Us</h3>
              <p className="text-gray-300">BITS Pilani, Goa Campus</p>
              <p className="text-gray-300">Zuarinagar, Goa 403726</p>
            </div>
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-gray-300 mb-6">
              Have questions about Zephyr 2025?
              We'd love to hear from you!
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default About;