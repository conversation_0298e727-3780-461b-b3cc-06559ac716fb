import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, Pause, Volume2, MessageSquare, Heart, Share2, Eye, Clock, Trophy, Star } from 'lucide-react';

const Live = () => {
  const [selectedMatch, setSelectedMatch] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  const liveMatches = [
    {
      id: 1,
      sport: 'Football',
      team1: 'DH1',
      team2: 'AH3',
      score: '2-1',
      time: '78:45',
      venue: 'Main Ground',
      viewers: 1247,
      status: 'live'
    },
    {
      id: 2,
      sport: 'Cricket',
      team1: 'CH6',
      team2: 'DH4',
      score: '156/4 (18.2)',
      time: 'Over 18',
      venue: 'Cricket Ground',
      viewers: 892,
      status: 'live'
    }
  ];

  const liveCommentary = [
    {
      id: 1,
      time: '78:45',
      text: 'GOAL! Amazing strike from DH1\'s striker! The crowd goes wild!',
      type: 'goal',
      author: 'Sports Commentator'
    },
    {
      id: 2,
      time: '76:30',
      text: 'Corner kick for DH1. This could be dangerous...',
      type: 'normal',
      author: 'Live Reporter'
    },
    {
      id: 3,
      time: '74:15',
      text: 'AH3 is pushing for an equalizer. The pressure is mounting!',
      type: 'normal',
      author: 'Sports Commentator'
    },
    {
      id: 4,
      time: '72:20',
      text: 'Yellow card for AH3 midfielder for a tactical foul.',
      type: 'card',
      author: 'Match Official'
    },
    {
      id: 5,
      time: '70:05',
      text: 'Substitution: DH1 brings on fresh legs in midfield.',
      type: 'substitution',
      author: 'Live Reporter'
    }
  ];

  const upcomingMatches = [
    {
      id: 3,
      sport: 'Basketball',
      team1: 'AH1',
      team2: 'CH7',
      time: '04:00 PM',
      venue: 'Basketball Court'
    },
    {
      id: 4,
      sport: 'Volleyball',
      team1: 'CH5',
      team2: 'DH3',
      time: '05:30 PM',
      venue: 'Volleyball Court'
    },
    {
      id: 5,
      sport: 'Table Tennis',
      team1: 'AH2',
      team2: 'CH4',
      time: '06:00 PM',
      venue: 'TT Hall'
    }
  ];

  const socialFeed = [
    {
      id: 1,
      user: '@zephyrbits',
      text: 'What a match! DH1 showing incredible teamwork! 🔥⚽ #Zephyr2025',
      likes: 45,
      time: '2m ago'
    },
    {
      id: 2,
      user: '@zephyrbits',
      text: 'AH3 needs to step up their game in the second half! Come on! 💪 #ZephyrFever',
      likes: 32,
      time: '5m ago'
    },
    {
      id: 3,
      user: '@zephyrbits',
      text: 'The atmosphere at the main ground is electric! ⚡ #LiveAtZephyr',
      likes: 78,
      time: '8m ago'
    }
  ];

  const getCommentaryTypeColor = (type: string) => {
    switch (type) {
      case 'goal':
        return 'border-l-green-500';
      case 'card':
        return 'border-l-yellow-500';
      case 'substitution':
        return 'border-l-blue-500';
      default:
        return 'border-l-gray-500';
    }
  };

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-red-400 to-pink-500 bg-clip-text text-transparent">
            Live Updates
          </h1>
          <p className="text-xl text-gray-300">Live commentary and scorecards</p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Live Feed */}
          <div className="lg:col-span-2 space-y-8">
            {/* Live Matches */}
            <motion.div
              className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <h2 className="text-2xl font-bold text-white">Live Matches</h2>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Eye className="w-4 h-4" />
                    <span className="text-sm">{liveMatches[selectedMatch]?.viewers} watching</span>
                  </div>
                </div>
              </div>

              {/* Match Selector */}
              <div className="p-4 bg-black/20 border-b border-white/10">
                <div className="flex space-x-2 overflow-x-auto">
                  {liveMatches.map((match, index) => (
                    <button
                      key={match.id}
                      onClick={() => setSelectedMatch(index)}
                      className={`flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                        selectedMatch === index
                          ? 'bg-red-500 text-white'
                          : 'bg-white/10 text-gray-300 hover:bg-white/20'
                      }`}
                    >
                      {match.sport}: {match.team1} vs {match.team2}
                    </button>
                  ))}
                </div>
              </div>

              {/* Live Match Display */}
              {liveMatches[selectedMatch] && (
                <div className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-3xl font-bold text-white mb-2">
                      {liveMatches[selectedMatch].team1} vs {liveMatches[selectedMatch].team2}
                    </h3>
                    <div className="text-4xl font-bold text-red-400 mb-2">
                      {liveMatches[selectedMatch].score}
                    </div>
                    <div className="flex items-center justify-center space-x-4 text-gray-300">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{liveMatches[selectedMatch].time}</span>
                      </div>
                      <span>•</span>
                      <span>{liveMatches[selectedMatch].venue}</span>
                    </div>
                  </div>

                  {/* Scorecard Display */}
                  <div className="relative bg-black/40 rounded-xl p-6 mb-4">
                    <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 to-pink-500/20 rounded-xl"></div>
                    <div className="relative z-10">
                      <div className="text-center mb-4">
                        <div className="text-4xl mb-2">📊</div>
                        <h3 className="text-xl font-bold text-white mb-2">Live Scorecard</h3>
                        <p className="text-gray-300">Real-time match statistics and scores</p>
                      </div>

                      {/* Match Stats */}
                      <div className="grid grid-cols-2 gap-4 mt-6">
                        <div className="text-center p-3 bg-black/20 rounded-lg">
                          <div className="text-lg font-bold text-white">{liveMatches[selectedMatch].team1}</div>
                          <div className="text-sm text-gray-400">Possession: 58%</div>
                          <div className="text-sm text-gray-400">Shots: 12</div>
                        </div>
                        <div className="text-center p-3 bg-black/20 rounded-lg">
                          <div className="text-lg font-bold text-white">{liveMatches[selectedMatch].team2}</div>
                          <div className="text-sm text-gray-400">Possession: 42%</div>
                          <div className="text-sm text-gray-400">Shots: 8</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Live Commentary */}
            <motion.div
              className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="p-6 border-b border-white/10">
                <h2 className="text-2xl font-bold text-white">Live Commentary</h2>
              </div>
              
              <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
                {liveCommentary.map((comment, index) => (
                  <motion.div
                    key={comment.id}
                    className={`p-4 bg-black/20 rounded-lg border-l-4 ${getCommentaryTypeColor(comment.type)}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <span className="text-sm font-medium text-cyan-400">{comment.time}</span>
                      <span className="text-xs text-gray-500">{comment.author}</span>
                    </div>
                    <p className="text-white">{comment.text}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Matches */}
            <motion.div
              className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <div className="p-6 border-b border-white/10">
                <h2 className="text-xl font-bold text-white">Next Up</h2>
              </div>
              
              <div className="p-6 space-y-4">
                {upcomingMatches.map((match, index) => (
                  <motion.div
                    key={match.id}
                    className="p-4 bg-black/20 rounded-lg border border-white/10 hover:border-white/20 transition-all"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                  >
                    <div className="text-sm text-cyan-400 mb-1">{match.sport}</div>
                    <div className="text-white font-medium mb-1">
                      {match.team1} vs {match.team2}
                    </div>
                    <div className="text-sm text-gray-400">
                      {match.time} • {match.venue}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Social Feed */}
            <motion.div
              className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              <div className="p-6 border-b border-white/10">
                <h2 className="text-xl font-bold text-white">Latest from @zephyrbits</h2>
              </div>
              
              <div className="p-6 space-y-4">
                {socialFeed.map((post, index) => (
                  <motion.div
                    key={post.id}
                    className="p-4 bg-black/20 rounded-lg border border-white/10"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <span className="text-sm font-medium text-cyan-400">{post.user}</span>
                      <span className="text-xs text-gray-500">{post.time}</span>
                    </div>
                    <p className="text-white text-sm mb-3">{post.text}</p>
                    <div className="flex items-center space-x-4">
                      <button className="flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors">
                        <Heart className="w-4 h-4" />
                        <span className="text-xs">{post.likes}</span>
                      </button>
                      <button className="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors">
                        <MessageSquare className="w-4 h-4" />
                        <span className="text-xs">Reply</span>
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
            >
              <div className="p-6 border-b border-white/10">
                <h2 className="text-xl font-bold text-white">Today's Stats</h2>
              </div>
              
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Matches Played</span>
                  <span className="text-white font-bold">8</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Goals Scored</span>
                  <span className="text-white font-bold">23</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Total Viewers</span>
                  <span className="text-white font-bold">3.2K</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Social Posts</span>
                  <span className="text-white font-bold">156</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Live;