import React from 'react';
import { motion } from 'framer-motion';
import { Music, Palette, Mic, Drama, Camera, Star, Calendar, MapPin, Users, Zap } from 'lucide-react';

const Cultural = () => {
  const culturalEvents = [
    {
      id: 1,
      name: 'Fashion Parade',
      category: 'Fashion',
      icon: '👗',
      description: 'Showcase your style and creativity on the ramp',
      date: '2025-01-21',
      time: '07:00 PM',
      venue: 'Main Auditorium',
      participants: 15,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'upcoming',
      color: 'from-pink-500 to-rose-600'
    },
    {
      id: 2,
      name: 'Irshaad',
      category: 'Poetry',
      icon: '📝',
      description: 'Express your thoughts through beautiful poetry',
      date: '2025-01-20',
      time: '06:00 PM',
      venue: 'Literature Hall',
      participants: 25,
      points: { gold: 75, silver: 50, bronze: 25 },
      status: 'ongoing',
      color: 'from-purple-500 to-indigo-600'
    },
    {
      id: 3,
      name: 'Dance Face-off',
      category: 'Dance',
      icon: '💃',
      description: 'Battle it out on the dance floor',
      date: '2025-01-22',
      time: '08:00 PM',
      venue: 'Main Auditorium',
      participants: 12,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'upcoming',
      color: 'from-cyan-500 to-blue-600'
    },
    {
      id: 4,
      name: 'Solo Singing',
      category: 'Music',
      icon: '🎤',
      description: 'Mesmerize the audience with your voice',
      date: '2025-01-19',
      time: '07:30 PM',
      venue: 'Music Room',
      participants: 30,
      points: { gold: 75, silver: 50, bronze: 25 },
      status: 'completed',
      color: 'from-green-500 to-emerald-600'
    },
    {
      id: 5,
      name: 'Group Dance',
      category: 'Dance',
      icon: '🕺',
      description: 'Synchronized moves, spectacular performances',
      date: '2025-01-23',
      time: '07:00 PM',
      venue: 'Main Auditorium',
      participants: 8,
      points: { gold: 125, silver: 100, bronze: 75 },
      status: 'upcoming',
      color: 'from-orange-500 to-red-600'
    },
    {
      id: 6,
      name: 'Stand-up Comedy',
      category: 'Comedy',
      icon: '😂',
      description: 'Make everyone laugh with your wit',
      date: '2025-01-20',
      time: '09:00 PM',
      venue: 'Comedy Club',
      participants: 18,
      points: { gold: 50, silver: 25, bronze: 15 },
      status: 'ongoing',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      id: 7,
      name: 'Art Exhibition',
      category: 'Visual Arts',
      icon: '🎨',
      description: 'Display your artistic masterpieces',
      date: '2025-01-19',
      time: '10:00 AM',
      venue: 'Art Gallery',
      participants: 22,
      points: { gold: 50, silver: 25, bronze: 15 },
      status: 'completed',
      color: 'from-teal-500 to-cyan-600'
    },
    {
      id: 8,
      name: 'Photography Contest',
      category: 'Photography',
      icon: '📸',
      description: 'Capture the perfect moment',
      date: '2025-01-21',
      time: '12:00 PM',
      venue: 'Campus Wide',
      participants: 35,
      points: { gold: 50, silver: 25, bronze: 15 },
      status: 'upcoming',
      color: 'from-slate-500 to-gray-600'
    },
    {
      id: 9,
      name: 'Drama Competition',
      category: 'Theatre',
      icon: '🎭',
      description: 'Bring stories to life on stage',
      date: '2025-01-22',
      time: '06:30 PM',
      venue: 'Theatre Hall',
      participants: 10,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'upcoming',
      color: 'from-red-500 to-pink-600'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing':
        return 'bg-green-500 animate-pulse';
      case 'upcoming':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-pink-400 to-purple-500 bg-clip-text text-transparent">
            Cultural Events
          </h1>
          <p className="text-xl text-gray-300">Unleash your artistic talents and creativity for points and glory</p>
        </motion.div>

        {/* Enhanced Featured Event */}
        <motion.div
          className="mb-16 relative overflow-hidden rounded-3xl bg-gradient-to-r from-pink-500/20 to-purple-600/20 backdrop-blur-sm border border-pink-500/30"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-purple-600/10"></div>
          <div className="relative z-10 p-12 text-center">
            <div className="text-7xl mb-6">🌟</div>
            <h2 className="text-5xl font-bold text-white mb-6">Grand Cultural Night</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              The biggest cultural extravaganza featuring performances from all hostels. 
              A night of music, dance, drama, and pure entertainment with maximum points at stake!
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center text-gray-300 mb-8">
              <div className="flex items-center space-x-2">
                <Calendar className="w-6 h-6 text-pink-400" />
                <span className="text-lg">January 24, 2025</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-6 h-6 text-pink-400" />
                <span className="text-lg">Main Auditorium</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="w-6 h-6 text-pink-400" />
                <span className="text-lg">200 Points for Winner</span>
              </div>
            </div>
            <button className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold py-4 px-10 rounded-full transition-all duration-300 hover:scale-105 shadow-2xl">
              Register Now
            </button>
          </div>
        </motion.div>

        {/* Enhanced Events Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {culturalEvents.map((event, index) => (
            <motion.div
              key={event.id}
              className="group relative bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:border-white/30 transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              whileHover={{ scale: 1.02, y: -10 }}
            >
              {/* Enhanced Status Badge */}
              <div className={`absolute top-6 right-6 px-4 py-2 ${getStatusColor(event.status)} rounded-full text-xs font-medium text-white shadow-lg`}>
                {event.status.toUpperCase()}
              </div>

              {/* Glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${event.color}/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>

              <div className="mb-6 relative z-10">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="text-5xl">{event.icon}</div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">{event.name}</h3>
                    <p className="text-sm text-gray-400">{event.category}</p>
                  </div>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">{event.description}</p>
              </div>

              <div className="space-y-3 mb-6 relative z-10">
                <div className="flex items-center space-x-3 text-gray-300">
                  <Calendar className="w-5 h-5 text-pink-400" />
                  <span className="text-sm">{event.date} at {event.time}</span>
                </div>
                
                <div className="flex items-center space-x-3 text-gray-300">
                  <MapPin className="w-5 h-5 text-pink-400" />
                  <span className="text-sm">{event.venue}</span>
                </div>
                
                <div className="flex items-center space-x-3 text-gray-300">
                  <Users className="w-5 h-5 text-pink-400" />
                  <span className="text-sm">{event.participants} participants</span>
                </div>
              </div>

              {/* Points breakdown */}
              <div className="mb-6 pt-4 border-t border-white/10 relative z-10">
                <div className="text-sm text-gray-400 mb-3">Points Distribution</div>
                <div className="flex justify-between text-sm">
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-white font-medium">{event.points.gold}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span className="text-white font-medium">{event.points.silver}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                    <span className="text-white font-medium">{event.points.bronze}</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 relative z-10">
                <button className={`flex-1 bg-gradient-to-r ${event.color} hover:scale-105 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg`}>
                  View Details
                </button>
                <button className="flex-1 bg-white/10 hover:bg-white/20 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm border border-white/20">
                  Register
                </button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Categories Summary */}
        <motion.div
          className="mt-20 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          {[
            { name: 'Music', icon: Music, count: 5, color: 'from-green-500 to-emerald-600' },
            { name: 'Dance', icon: Drama, count: 8, color: 'from-cyan-500 to-blue-600' },
            { name: 'Art', icon: Palette, count: 6, color: 'from-teal-500 to-cyan-600' },
            { name: 'Comedy', icon: Mic, count: 3, color: 'from-yellow-500 to-orange-500' },
            { name: 'Theatre', icon: Drama, count: 4, color: 'from-red-500 to-pink-600' },
            { name: 'Photography', icon: Camera, count: 2, color: 'from-slate-500 to-gray-600' }
          ].map((category, index) => (
            <motion.div
              key={category.name}
              className={`p-6 bg-gradient-to-br ${category.color}/10 rounded-3xl backdrop-blur-sm border border-white/10 text-center hover:border-white/20 transition-all duration-300 group hover:scale-105`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
            >
              <div className="relative mb-4">
                <category.icon className={`w-10 h-10 mx-auto bg-gradient-to-r ${category.color} bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300`} />
                <div className={`absolute inset-0 bg-gradient-to-r ${category.color}/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
              </div>
              <div className="text-white font-medium text-lg">{category.name}</div>
              <div className="text-sm text-gray-400">{category.count} events</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default Cultural;