// import React, { useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Trophy, Calendar, Users, ArrowRight, Star, Sparkles, Target } from 'lucide-react';
import zephyrLogo from '../zephyr.jpg';

const Home = () => {
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, -25]);
  const opacity = useTransform(scrollY, [0, 200], [1, 0.8]);

  return (
    <div className="min-h-screen">
      {/* Enhanced Hero Section */}
      <section className="relative pt-20 pb-32 overflow-hidden">
        <motion.div
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
          style={{ y: y1, opacity }}
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            {/* Professional Logo */}
            <motion.div
              className="inline-block mb-8"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{
                duration: 0.6,
                ease: "easeOut"
              }}
            >
              <div className="relative group">
                <div className="w-40 h-40 mx-auto bg-gradient-to-r from-orange-400 via-red-500 to-yellow-500 rounded-3xl flex items-center justify-center relative overflow-hidden shadow-2xl">
                  <img
                    src={zephyrLogo}
                    alt="Zephyr Logo"
                    className="w-32 h-32 object-cover rounded-2xl z-10 transition-transform duration-300 group-hover:scale-105"
                  />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400/10 via-red-500/10 to-yellow-500/10"></div>
                </div>
                {/* Subtle shadow */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-500 to-yellow-500 rounded-3xl blur-xl opacity-30 group-hover:opacity-40 transition-opacity duration-300"></div>
              </div>
            </motion.div>

            {/* Professional Title */}
            <motion.h1
              className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-bold mb-6 bg-gradient-to-r from-orange-400 via-red-400 to-yellow-500 bg-clip-text text-transparent relative leading-tight"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6, ease: "easeOut" }}
            >
              ZEPHYR 2025
            </motion.h1>

            {/* Professional Subtitle */}
            <motion.p
              className="text-xl sm:text-2xl md:text-3xl text-gray-300 mb-4 max-w-4xl mx-auto font-light leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              BITS Goa's Premier Inter-Hostel Festival
            </motion.p>

            <motion.p
              className="text-lg text-orange-400 mb-12 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              Where 18 hostels compete across 30+ events in the ultimate showcase of talent, teamwork, and triumph
            </motion.p>

            {/* Responsive Stats Bar */}
            <motion.div
              className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 mb-12 text-base sm:text-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <div className="flex items-center space-x-2 text-orange-400">
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="font-semibold">30+ Events</span>
              </div>
              <div className="hidden sm:block w-px h-6 bg-gray-600"></div>
              <div className="flex items-center space-x-2 text-red-400">
                <Target className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="font-semibold">18 Hostels</span>
              </div>
              <div className="hidden sm:block w-px h-6 bg-gray-600"></div>
              <div className="flex items-center space-x-2 text-yellow-400">
                <Star className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="font-semibold">3 Days</span>
              </div>
            </motion.div>

            {/* Enhanced CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              <Link
                to="/leaderboard"
                className="professional-button group relative px-8 sm:px-10 py-3 sm:py-4 bg-gradient-to-r from-orange-500 to-red-600 text-white font-semibold text-base sm:text-lg hover:scale-105 flex items-center space-x-3"
              >
                <Trophy className="w-5 h-5 sm:w-6 sm:h-6 z-10 group-hover:rotate-12 transition-transform duration-300" />
                <span className="z-10">Live Leaderboard</span>
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform z-10" />
              </Link>

              <Link
                to="/live"
                className="professional-button group relative px-8 sm:px-10 py-3 sm:py-4 bg-gradient-to-r from-yellow-500 to-orange-600 text-white font-semibold text-base sm:text-lg hover:scale-105 flex items-center space-x-3"
              >
                <Users className="w-5 h-5 sm:w-6 sm:h-6 z-10 group-hover:scale-110 transition-transform duration-300" />
                <span className="z-10">Live Updates</span>
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform z-10" />
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Subtle decorative elements with parallax */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            className="absolute top-20 left-10 w-32 h-32 bg-orange-500/5 rounded-full blur-xl"
            style={{ y: y2 }}
          ></motion.div>
          <motion.div
            className="absolute bottom-20 right-10 w-40 h-40 bg-yellow-500/5 rounded-full blur-xl"
            style={{ y: useTransform(scrollY, [0, 300], [0, 30]) }}
          ></motion.div>

          {/* Professional floating element that slides in from left */}
          <motion.div
            className="absolute top-1/2 -left-20 w-16 h-16 bg-gradient-to-r from-orange-400/20 to-red-400/20 rounded-2xl backdrop-blur-sm border border-orange-400/30"
            style={{
              x: useTransform(scrollY, [0, 200], [-100, 50]),
              opacity: useTransform(scrollY, [0, 100, 200], [0, 0.7, 0.3])
            }}
          ></motion.div>
        </div>
      </section>

      {/* Professional Stats Section with Parallax */}
      <motion.section
        className="py-24 bg-black/20 backdrop-blur-sm border-y border-white/10 relative overflow-hidden"
        style={{ y: useTransform(scrollY, [300, 800], [0, -50]) }}
      >
        {/* Subtle background element */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 via-transparent to-yellow-500/5"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.6,
              staggerChildren: 0.15,
              ease: "easeOut"
            }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div
              className="text-center p-8 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-3xl backdrop-blur-sm border border-orange-500/20 hover:border-orange-500/40 transition-all duration-300 group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className="relative mb-6">
                <Trophy className="w-16 h-16 mx-auto text-orange-400 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-orange-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <motion.div
                className="text-5xl font-bold text-white mb-2"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3, type: "spring", bounce: 0.4 }}
                viewport={{ once: true }}
              >
                30+
              </motion.div>
              <div className="text-gray-400 text-lg">Competitions</div>
            </motion.div>

            <motion.div
              className="text-center p-8 bg-gradient-to-br from-red-500/10 to-yellow-500/10 rounded-3xl backdrop-blur-sm border border-red-500/20 hover:border-red-500/40 transition-all duration-300 group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className="relative mb-6">
                <Users className="w-16 h-16 mx-auto text-red-400 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-red-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <motion.div
                className="text-5xl font-bold text-white mb-2"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4, type: "spring", bounce: 0.4 }}
                viewport={{ once: true }}
              >
                18
              </motion.div>
              <div className="text-gray-400 text-lg">Hostels</div>
            </motion.div>

            <motion.div
              className="text-center p-8 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl backdrop-blur-sm border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className="relative mb-6">
                <Calendar className="w-16 h-16 mx-auto text-yellow-400 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-yellow-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <motion.div
                className="text-5xl font-bold text-white mb-2"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.5, type: "spring", bounce: 0.4 }}
                viewport={{ once: true }}
              >
                3
              </motion.div>
              <div className="text-gray-400 text-lg">Days of Action</div>
            </motion.div>
          </motion.div>
        </div>

        {/* Professional accent element */}
        <motion.div
          className="absolute bottom-10 left-10 w-24 h-1 bg-gradient-to-r from-orange-400 to-yellow-500 rounded-full"
          initial={{ width: 0, opacity: 0 }}
          whileInView={{ width: 96, opacity: 1 }}
          transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
          viewport={{ once: true }}
        ></motion.div>
      </motion.section>

      {/* Professional Quick Links with Parallax */}
      <motion.section
        className="py-24 relative overflow-hidden"
        style={{ y: useTransform(scrollY, [600, 1200], [0, -30]) }}
      >
        {/* Subtle sliding element from left */}
        <motion.div
          className="absolute top-1/3 -left-32 w-64 h-2 bg-gradient-to-r from-orange-400/30 to-transparent rounded-full"
          style={{
            x: useTransform(scrollY, [400, 800], [-200, 100]),
            opacity: useTransform(scrollY, [400, 600, 800], [0, 0.8, 0.2])
          }}
        ></motion.div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-orange-400 to-yellow-500 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Explore Zephyr 2025
          </motion.h2>
          
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, staggerChildren: 0.15 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            {[
              { title: 'Leaderboard', desc: 'Live rankings and hostel standings', link: '/leaderboard', color: 'from-orange-500 to-red-500', icon: '🏆' },
              { title: 'Fixtures & Results', desc: 'Event schedules and results', link: '/fixtures', color: 'from-blue-500 to-cyan-500', icon: '📅' },
              { title: 'Live Updates', desc: 'Live commentary and scorecards', link: '/live', color: 'from-green-500 to-emerald-500', icon: '�' },
              { title: 'Announcements', desc: 'Latest news and updates', link: '/announcements', color: 'from-yellow-500 to-orange-500', icon: '📢' },
              { title: 'About Zephyr', desc: 'Learn more about the festival', link: '/about', color: 'from-purple-500 to-indigo-500', icon: '⚡' },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.1,
                  ease: "easeOut"
                }}
                viewport={{ once: true, margin: "-50px" }}
                className="group"
              >
                <Link
                  to={item.link}
                  className={`professional-card block p-6 md:p-8 bg-gradient-to-br ${item.color}/10 group relative overflow-hidden hover:scale-105 transition-all duration-300`}
                >
                  <div className="relative z-10">
                    <div className="text-3xl md:text-4xl mb-4 transition-transform duration-300 group-hover:scale-110">{item.icon}</div>
                    <h3 className={`text-lg md:text-xl font-bold mb-3 bg-gradient-to-r ${item.color} bg-clip-text text-transparent`}>
                      {item.title}
                    </h3>
                    <p className="text-gray-400 mb-4 leading-relaxed text-sm">{item.desc}</p>
                    <div className="flex items-center text-sm text-gray-300 group-hover:text-white transition-colors">
                      <span className="font-medium">Explore</span>
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
};

export default Home;