// import React, { useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Trophy, Calendar, Users, Zap, ArrowRight, Star, Sparkles, Target } from 'lucide-react';

const Home = () => {
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, -25]);
  const opacity = useTransform(scrollY, [0, 200], [1, 0.8]);

  return (
    <div className="min-h-screen">
      {/* Enhanced Hero Section */}
      <section className="relative pt-20 pb-32 overflow-hidden">
        <motion.div
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
          style={{ y: y1, opacity }}
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            {/* Enhanced Logo Animation */}
            <motion.div
              className="inline-block mb-8"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{
                duration: 0.8,
                type: "spring",
                stiffness: 100,
                damping: 15
              }}
            >
              <div className="relative">
                <div className="w-40 h-40 mx-auto bg-gradient-to-r from-cyan-400 via-blue-500 to-fuchsia-500 rounded-3xl flex items-center justify-center relative overflow-hidden">
                  <Zap className="w-20 h-20 text-white z-10" />
                  {/* Animated background pattern */}
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 via-blue-500/20 to-fuchsia-500/20 animate-pulse"></div>
                  {/* Scanning lines */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/10 to-transparent transform -skew-x-12 animate-pulse"></div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-blue-500 to-fuchsia-500 rounded-3xl blur-2xl opacity-60 animate-pulse"></div>
                {/* Orbital rings */}
                <div className="absolute inset-0 border-2 border-cyan-400/30 rounded-3xl animate-spin" style={{ animationDuration: '10s' }}></div>
                <div className="absolute inset-0 border border-fuchsia-400/20 rounded-3xl animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
              </div>
            </motion.div>

            {/* Enhanced Title */}
            <motion.h1 
              className="text-7xl md:text-9xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-blue-400 to-fuchsia-500 bg-clip-text text-transparent relative"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.8 }}
            >
              ZEPHYR 2025
              {/* Glitch effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-blue-400 to-fuchsia-500 bg-clip-text text-transparent opacity-0 animate-pulse"></div>
            </motion.h1>
            
            {/* Enhanced Subtitle */}
            <motion.p 
              className="text-2xl md:text-3xl text-gray-300 mb-4 max-w-4xl mx-auto font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              BITS Goa's Premier Inter-Hostel Festival
            </motion.p>

            <motion.p 
              className="text-lg text-cyan-400 mb-12 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              Where 17 hostels compete across 30+ events in the ultimate showcase of talent, teamwork, and triumph
            </motion.p>

            {/* Enhanced Stats Bar */}
            <motion.div
              className="flex items-center justify-center space-x-8 mb-12 text-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <div className="flex items-center space-x-2 text-cyan-400">
                <Sparkles className="w-5 h-5" />
                <span className="font-semibold">30+ Events</span>
              </div>
              <div className="w-px h-6 bg-gray-600"></div>
              <div className="flex items-center space-x-2 text-fuchsia-400">
                <Target className="w-5 h-5" />
                <span className="font-semibold">17 Hostels</span>
              </div>
              <div className="w-px h-6 bg-gray-600"></div>
              <div className="flex items-center space-x-2 text-emerald-400">
                <Star className="w-5 h-5" />
                <span className="font-semibold">5 Days</span>
              </div>
            </motion.div>

            {/* Enhanced CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              <Link
                to="/leaderboard"
                className="group relative px-10 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-2xl text-white font-semibold text-lg transition-all duration-300 hover:scale-105 flex items-center space-x-3 overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Trophy className="w-6 h-6 z-10" />
                <span className="z-10">Live Leaderboard</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform z-10" />
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-opacity"></div>
              </Link>
              
              <Link
                to="/live"
                className="group relative px-10 py-4 bg-gradient-to-r from-fuchsia-500 to-purple-600 rounded-2xl text-white font-semibold text-lg transition-all duration-300 hover:scale-105 flex items-center space-x-3 overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-fuchsia-400 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Users className="w-6 h-6 z-10" />
                <span className="z-10">Live Updates</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform z-10" />
                <div className="absolute inset-0 bg-gradient-to-r from-fuchsia-500 to-purple-600 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-opacity"></div>
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Subtle decorative elements with parallax */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            className="absolute top-20 left-10 w-32 h-32 bg-cyan-500/5 rounded-full blur-xl"
            style={{ y: y2 }}
          ></motion.div>
          <motion.div
            className="absolute bottom-20 right-10 w-40 h-40 bg-fuchsia-500/5 rounded-full blur-xl"
            style={{ y: useTransform(scrollY, [0, 300], [0, 30]) }}
          ></motion.div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="py-24 bg-black/20 backdrop-blur-sm border-y border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.6,
              staggerChildren: 0.2,
              ease: "easeOut"
            }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div
              className="text-center p-8 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-3xl backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300 group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className="relative mb-6">
                <Trophy className="w-16 h-16 mx-auto text-cyan-400 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <motion.div
                className="text-5xl font-bold text-white mb-2"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3, type: "spring", bounce: 0.4 }}
                viewport={{ once: true }}
              >
                30+
              </motion.div>
              <div className="text-gray-400 text-lg">Competitions</div>
            </motion.div>

            <motion.div
              className="text-center p-8 bg-gradient-to-br from-fuchsia-500/10 to-purple-500/10 rounded-3xl backdrop-blur-sm border border-fuchsia-500/20 hover:border-fuchsia-500/40 transition-all duration-300 group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className="relative mb-6">
                <Users className="w-16 h-16 mx-auto text-fuchsia-400 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-fuchsia-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <motion.div
                className="text-5xl font-bold text-white mb-2"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4, type: "spring", bounce: 0.4 }}
                viewport={{ once: true }}
              >
                17
              </motion.div>
              <div className="text-gray-400 text-lg">Hostels</div>
            </motion.div>

            <motion.div
              className="text-center p-8 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-3xl backdrop-blur-sm border border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300 group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <div className="relative mb-6">
                <Calendar className="w-16 h-16 mx-auto text-emerald-400 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-emerald-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <motion.div
                className="text-5xl font-bold text-white mb-2"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.5, type: "spring", bounce: 0.4 }}
                viewport={{ once: true }}
              >
                5
              </motion.div>
              <div className="text-gray-400 text-lg">Days of Action</div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Quick Links */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h2 
            className="text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Explore Zephyr 2025
          </motion.h2>
          
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, staggerChildren: 0.15 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            {[
              { title: 'Leaderboard', desc: 'Live rankings and hostel standings', link: '/leaderboard', color: 'from-orange-500 to-red-500', icon: '🏆' },
              { title: 'Fixtures & Results', desc: 'Sports, Cultural & Technical events schedule', link: '/fixtures', color: 'from-blue-500 to-cyan-500', icon: '�' },
              { title: 'Live Updates', desc: 'Real-time scores and commentary', link: '/live', color: 'from-green-500 to-emerald-500', icon: '📡' },
              { title: 'Announcements', desc: 'Latest news and updates', link: '/announcements', color: 'from-yellow-500 to-orange-500', icon: '�' },
              { title: 'About Zephyr', desc: 'Learn more about the festival', link: '/about', color: 'from-purple-500 to-indigo-500', icon: '⚡' },
            ].map((item, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.03, y: -8 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 50, rotateX: 15 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100,
                  damping: 15
                }}
                viewport={{ once: true, margin: "-50px" }}
              >
                <Link
                  to={item.link}
                  className={`block p-8 bg-gradient-to-br ${item.color}/10 backdrop-blur-sm border border-white/10 rounded-3xl hover:border-white/30 transition-all duration-300 group relative overflow-hidden`}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="text-4xl mb-4">{item.icon}</div>
                    <h3 className={`text-2xl font-bold mb-3 bg-gradient-to-r ${item.color} bg-clip-text text-transparent`}>
                      {item.title}
                    </h3>
                    <p className="text-gray-400 mb-6 leading-relaxed">{item.desc}</p>
                    <div className="flex items-center text-sm text-gray-300 group-hover:text-white transition-colors">
                      <span className="font-medium">Explore</span>
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-2 transition-transform" />
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;