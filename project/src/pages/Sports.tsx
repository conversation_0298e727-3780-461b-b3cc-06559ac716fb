import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, Calendar, Users, MapPin, Clock, Filter, Star, Zap } from 'lucide-react';

const Sports = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const sportsEvents = [
    {
      id: 1,
      name: 'Football',
      category: 'team',
      tier: 'gold',
      status: 'ongoing',
      participants: 16,
      venue: 'Main Ground',
      date: '2025-01-20',
      time: '09:00 AM',
      description: '11v11 knockout tournament',
      image: '⚽',
      points: { gold: 100, silver: 75, bronze: 50 }
    },
    {
      id: 2,
      name: 'Cricket',
      category: 'team',
      tier: 'gold',
      status: 'upcoming',
      participants: 12,
      venue: 'Cricket Ground',
      date: '2025-01-21',
      time: '08:00 AM',
      description: 'T20 format matches',
      image: '🏏',
      points: { gold: 100, silver: 75, bronze: 50 }
    },
    {
      id: 3,
      name: 'Basketball',
      category: 'team',
      tier: 'silver',
      status: 'ongoing',
      participants: 14,
      venue: 'Basketball Court',
      date: '2025-01-20',
      time: '10:00 AM',
      description: '5v5 tournament',
      image: '🏀',
      points: { gold: 75, silver: 50, bronze: 25 }
    },
    {
      id: 4,
      name: 'Badminton',
      category: 'individual',
      tier: 'silver',
      status: 'completed',
      participants: 32,
      venue: 'Sports Complex',
      date: '2025-01-19',
      time: '09:00 AM',
      description: 'Singles and doubles',
      image: '🏸',
      points: { gold: 75, silver: 50, bronze: 25 }
    },
    {
      id: 5,
      name: 'Swimming',
      category: 'individual',
      tier: 'gold',
      status: 'upcoming',
      participants: 28,
      venue: 'Swimming Pool',
      date: '2025-01-22',
      time: '07:00 AM',
      description: 'Multiple stroke categories',
      image: '🏊',
      points: { gold: 100, silver: 75, bronze: 50 }
    },
    {
      id: 6,
      name: 'Table Tennis',
      category: 'individual',
      tier: 'silver',
      status: 'ongoing',
      participants: 24,
      venue: 'TT Hall',
      date: '2025-01-20',
      time: '02:00 PM',
      description: 'Singles tournament',
      image: '🏓',
      points: { gold: 75, silver: 50, bronze: 25 }
    },
    {
      id: 7,
      name: 'Volleyball',
      category: 'team',
      tier: 'silver',
      status: 'upcoming',
      participants: 10,
      venue: 'Volleyball Court',
      date: '2025-01-21',
      time: '03:00 PM',
      description: '6v6 matches',
      image: '🏐',
      points: { gold: 75, silver: 50, bronze: 25 }
    },
    {
      id: 8,
      name: 'Chess',
      category: 'individual',
      tier: 'bronze',
      status: 'ongoing',
      participants: 32,
      venue: 'Activity Room',
      date: '2025-01-20',
      time: '11:00 AM',
      description: 'Swiss system tournament',
      image: '♟️',
      points: { gold: 50, silver: 25, bronze: 15 }
    },
    {
      id: 9,
      name: 'Carrom',
      category: 'individual',
      tier: 'bronze',
      status: 'completed',
      participants: 20,
      venue: 'Recreation Room',
      date: '2025-01-19',
      time: '02:00 PM',
      description: 'Singles and doubles',
      image: '🎯',
      points: { gold: 50, silver: 25, bronze: 15 }
    },
    {
      id: 10,
      name: 'Tug of War',
      category: 'team',
      tier: 'platinum',
      status: 'upcoming',
      participants: 17,
      venue: 'Central Ground',
      date: '2025-01-23',
      time: '05:00 PM',
      description: 'All hostels participate',
      image: '🪢',
      points: { gold: 150, silver: 100, bronze: 75 }
    }
  ];

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return 'from-purple-500 to-indigo-600';
      case 'gold':
        return 'from-yellow-400 to-orange-500';
      case 'silver':
        return 'from-gray-300 to-gray-500';
      case 'bronze':
        return 'from-orange-400 to-red-500';
      default:
        return 'from-blue-400 to-blue-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing':
        return 'bg-green-500 animate-pulse';
      case 'upcoming':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const filteredEvents = sportsEvents.filter(event => {
    const categoryMatch = selectedCategory === 'all' || event.category === selectedCategory;
    const statusMatch = selectedStatus === 'all' || event.status === selectedStatus;
    return categoryMatch && statusMatch;
  });

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Sports Events
          </h1>
          <p className="text-xl text-gray-300">Compete in 30+ sporting competitions for glory and points</p>
        </motion.div>

        {/* Enhanced Filters */}
        <motion.div
          className="flex flex-wrap gap-4 mb-12 p-8 bg-black/20 backdrop-blur-sm rounded-3xl border border-white/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center space-x-3">
            <Filter className="w-6 h-6 text-cyan-400" />
            <span className="text-white font-medium text-lg">Filters:</span>
          </div>
          
          <div className="flex flex-wrap gap-3">
            {['all', 'team', 'individual'].map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white shadow-lg'
                    : 'bg-white/10 text-gray-300 hover:bg-white/20'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
          
          <div className="flex flex-wrap gap-3">
            {['all', 'ongoing', 'upcoming', 'completed'].map(status => (
              <button
                key={status}
                onClick={() => setSelectedStatus(status)}
                className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                  selectedStatus === status
                    ? 'bg-gradient-to-r from-fuchsia-500 to-purple-600 text-white shadow-lg'
                    : 'bg-white/10 text-gray-300 hover:bg-white/20'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Sports Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {filteredEvents.map((event, index) => (
            <motion.div
              key={event.id}
              className="group relative bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:border-white/30 transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              whileHover={{ scale: 1.02, y: -10 }}
            >
              {/* Enhanced Tier Badge */}
              <div className={`absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r ${getTierColor(event.tier)} rounded-full flex items-center justify-center border-4 border-white/20 shadow-2xl`}>
                <Trophy className="w-8 h-8 text-white" />
              </div>

              {/* Enhanced Status Badge */}
              <div className={`absolute top-6 left-6 px-4 py-2 ${getStatusColor(event.status)} rounded-full text-xs font-medium text-white shadow-lg`}>
                {event.status.toUpperCase()}
              </div>

              {/* Glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${getTierColor(event.tier)}/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>

              <div className="mt-12 relative z-10">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="text-5xl">{event.image}</div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">{event.name}</h3>
                    <p className="text-gray-400 text-sm">{event.description}</p>
                  </div>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex items-center space-x-3 text-gray-300">
                    <Users className="w-5 h-5 text-cyan-400" />
                    <span className="text-sm">{event.participants} participants</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-gray-300">
                    <MapPin className="w-5 h-5 text-cyan-400" />
                    <span className="text-sm">{event.venue}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-gray-300">
                    <Calendar className="w-5 h-5 text-cyan-400" />
                    <span className="text-sm">{event.date}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-gray-300">
                    <Clock className="w-5 h-5 text-cyan-400" />
                    <span className="text-sm">{event.time}</span>
                  </div>
                </div>

                {/* Points breakdown */}
                <div className="mb-6 pt-4 border-t border-white/10">
                  <div className="text-sm text-gray-400 mb-3">Points Distribution</div>
                  <div className="flex justify-between text-sm">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-white font-medium">{event.points.gold}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                      <span className="text-white font-medium">{event.points.silver}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                      <span className="text-white font-medium">{event.points.bronze}</span>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg">
                    View Details
                  </button>
                  <button className="flex-1 bg-gradient-to-r from-fuchsia-500 to-purple-600 hover:from-fuchsia-600 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg">
                    Live Scores
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Stats Summary */}
        <motion.div
          className="mt-20 grid grid-cols-1 md:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="p-8 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-3xl backdrop-blur-sm border border-purple-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Star className="w-12 h-12 mx-auto text-purple-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">1</div>
            <div className="text-gray-400">Platinum Events</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl backdrop-blur-sm border border-yellow-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Trophy className="w-12 h-12 mx-auto text-yellow-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-yellow-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">11</div>
            <div className="text-gray-400">Gold Events</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-gray-400/10 to-gray-600/10 rounded-3xl backdrop-blur-sm border border-gray-400/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Medal className="w-12 h-12 mx-auto text-gray-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-gray-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">10</div>
            <div className="text-gray-400">Silver Events</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-3xl backdrop-blur-sm border border-orange-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Zap className="w-12 h-12 mx-auto text-orange-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-orange-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">8</div>
            <div className="text-gray-400">Bronze Events</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Sports;