import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, TrendingUp, TrendingDown, Minus, Crown, Star, Zap } from 'lucide-react';

const Leaderboard = () => {
  const [sortBy, setSortBy] = useState('points');

  // Mock data for hostels with points instead of money
  const hostels = [
    { name: 'AH1', points: 485, medals: { first: 8, second: 12 }, trend: 'up', position: 1, gender: 'boys' },
    { name: 'AH2', points: 472, medals: { first: 7, second: 14 }, trend: 'up', position: 2, gender: 'boys' },
    { name: 'CH4', points: 458, medals: { first: 9, second: 10 }, trend: 'down', position: 3, gender: 'girls' },
    { name: 'DH1', points: 441, medals: { first: 6, second: 13 }, trend: 'up', position: 4, gender: 'boys' },
    { name: 'AH3', points: 425, medals: { first: 5, second: 15 }, trend: 'same', position: 5, gender: 'boys' },
    { name: 'CH5', points: 418, medals: { first: 7, second: 11 }, trend: 'up', position: 6, gender: 'girls' },
    { name: 'DH2', points: 395, medals: { first: 4, second: 12 }, trend: 'down', position: 7, gender: 'boys' },
    { name: 'AH4', points: 387, medals: { first: 6, second: 9 }, trend: 'same', position: 8, gender: 'boys' },
    { name: 'CH6', points: 374, medals: { first: 5, second: 10 }, trend: 'up', position: 9, gender: 'girls' },
    { name: 'DH3', points: 361, medals: { first: 3, second: 11 }, trend: 'down', position: 10, gender: 'boys' },
    { name: 'AH5', points: 348, medals: { first: 4, second: 8 }, trend: 'same', position: 11, gender: 'boys' },
    { name: 'CH7', points: 335, medals: { first: 3, second: 9 }, trend: 'up', position: 12, gender: 'girls' },
    { name: 'DH4', points: 322, medals: { first: 2, second: 10 }, trend: 'down', position: 13, gender: 'boys' },
    { name: 'AH6', points: 308, medals: { first: 3, second: 7 }, trend: 'same', position: 14, gender: 'boys' },
    { name: 'AH7', points: 295, medals: { first: 2, second: 8 }, trend: 'down', position: 15, gender: 'boys' },
    { name: 'AH8', points: 281, medals: { first: 1, second: 9 }, trend: 'up', position: 16, gender: 'boys' },
    { name: 'AH9', points: 267, medals: { first: 2, second: 6 }, trend: 'down', position: 17, gender: 'boys' },
    { name: 'CH1', points: 250, medals: { first: 1, second: 5 }, trend: 'up', position: 18, gender: 'boys' },
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-400" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPositionColor = (position: number) => {
    if (position === 1) return 'from-yellow-400 to-yellow-600';
    if (position === 2) return 'from-gray-300 to-gray-500';
    if (position === 3) return 'from-orange-400 to-orange-600';
    return 'from-blue-400 to-blue-600';
  };

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Live Leaderboard
          </h1>
          <p className="text-xl text-gray-300">Real-time hostel rankings and points</p>
        </motion.div>

        {/* Enhanced Top 3 Podium */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {hostels.slice(0, 3).map((hostel, index) => (
            <motion.div
              key={hostel.name}
              className={`relative p-8 bg-gradient-to-br ${getPositionColor(hostel.position)}/10 backdrop-blur-sm border border-white/20 rounded-3xl text-center group hover:scale-105 transition-all duration-300 ${
                index === 0 ? 'md:order-2 md:scale-110' : index === 1 ? 'md:order-1' : 'md:order-3'
              }`}
              whileHover={{ y: -10 }}
              transition={{ type: "spring", bounce: 0.4 }}
            >
              {/* Enhanced position badge */}
              <div className="absolute -top-6 left-1/2 transform -translate-x-1/2">
                <div className={`w-16 h-16 bg-gradient-to-r ${getPositionColor(hostel.position)} rounded-full flex items-center justify-center border-4 border-white/20 shadow-2xl`}>
                  {hostel.position === 1 ? (
                    <Crown className="w-8 h-8 text-white" />
                  ) : (
                    <span className="text-white font-bold text-xl">{hostel.position}</span>
                  )}
                </div>
              </div>
              
              {/* Glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${getPositionColor(hostel.position)}/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
              
              <div className="mt-8 relative z-10">
                <h3 className="text-3xl font-bold text-white mb-2">{hostel.name}</h3>
                <p className="text-gray-300 text-sm mb-6">{hostel.fullName}</p>
                <div className="text-4xl font-bold text-white mb-2">{hostel.points}</div>
                <div className="text-gray-400 mb-6">Points</div>
                
                {/* Enhanced medal display */}
                <div className="flex justify-center space-x-4 mb-4">
                  <div className="flex items-center space-x-1">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                      <span className="text-xs font-bold text-white">{hostel.medals.gold}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center shadow-lg">
                      <span className="text-xs font-bold text-white">{hostel.medals.silver}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center shadow-lg">
                      <span className="text-xs font-bold text-white">{hostel.medals.bronze}</span>
                    </div>
                  </div>
                </div>

                {/* Trend indicator */}
                <div className="flex items-center justify-center">
                  {getTrendIcon(hostel.trend)}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Full Leaderboard */}
        <motion.div
          className="bg-black/20 backdrop-blur-sm rounded-3xl border border-white/10 overflow-hidden shadow-2xl"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="p-8 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-fuchsia-500/10">
            <h2 className="text-3xl font-bold text-white mb-6">Complete Rankings</h2>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setSortBy('points')}
                className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                  sortBy === 'points' 
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white shadow-lg' 
                    : 'bg-white/10 text-gray-300 hover:bg-white/20'
                }`}
              >
                By Points
              </button>
              <button
                onClick={() => setSortBy('medals')}
                className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                  sortBy === 'medals' 
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white shadow-lg' 
                    : 'bg-white/10 text-gray-300 hover:bg-white/20'
                }`}
              >
                By Medals
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-8 py-6 text-left text-sm font-medium text-gray-300">Rank</th>
                  <th className="px-8 py-6 text-left text-sm font-medium text-gray-300">Hostel</th>
                  <th className="px-8 py-6 text-left text-sm font-medium text-gray-300">Points</th>
                  <th className="px-8 py-6 text-left text-sm font-medium text-gray-300">Medals</th>
                  <th className="px-8 py-6 text-left text-sm font-medium text-gray-300">Trend</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {hostels.map((hostel, index) => (
                  <motion.tr
                    key={hostel.name}
                    className="hover:bg-white/5 transition-all duration-300 group"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                  >
                    <td className="px-8 py-6">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 bg-gradient-to-r ${getPositionColor(hostel.position)} rounded-xl flex items-center justify-center text-white font-bold shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                          {hostel.position}
                        </div>
                      </div>
                    </td>
                    <td className="px-8 py-6">
                      <div>
                        <div className="text-white font-medium text-lg">{hostel.name}</div>
                        <div className={`inline-flex items-center space-x-1 mt-2 px-3 py-1 rounded-full text-xs ${
                          hostel.gender === 'boys' ? 'bg-blue-500/20 text-blue-400' : 'bg-pink-500/20 text-pink-400'
                        }`}>
                          <Star className="w-3 h-3" />
                          <span>{hostel.gender === 'boys' ? 'Boys' : 'Girls'}</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-8 py-6">
                      <div className="text-3xl font-bold text-white">{hostel.points}</div>
                    </td>
                    <td className="px-8 py-6">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <Medal className="w-5 h-5 text-yellow-500" />
                          <span className="text-white font-medium">{hostel.medals.first}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Medal className="w-5 h-5 text-gray-400" />
                          <span className="text-white font-medium">{hostel.medals.second}</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-8 py-6">
                      <div className="flex items-center">
                        {getTrendIcon(hostel.trend)}
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Enhanced Stats Summary */}
        <motion.div
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="p-8 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-3xl backdrop-blur-sm border border-cyan-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Zap className="w-12 h-12 mx-auto text-cyan-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">
              {hostels.reduce((acc, hostel) => acc + hostel.points, 0)}
            </div>
            <div className="text-gray-400">Total Points</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl backdrop-blur-sm border border-yellow-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Medal className="w-12 h-12 mx-auto text-yellow-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-yellow-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">
              {hostels.reduce((acc, hostel) => acc + hostel.medals.first + hostel.medals.second, 0)}
            </div>
            <div className="text-gray-400">Total Medals</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-3xl backdrop-blur-sm border border-green-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Star className="w-12 h-12 mx-auto text-green-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-green-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">17</div>
            <div className="text-gray-400">Competing Hostels</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Leaderboard;