import React from 'react';
import { motion } from 'framer-motion';
import { Code, Cpu, Zap, Gamepad2, Lightbulb, Trophy, Calendar, MapPin, Users, Clock, Star } from 'lucide-react';

const Technical = () => {
  const technicalEvents = [
    {
      id: 1,
      name: 'CodeWars',
      category: 'Programming',
      icon: Code,
      description: 'Competitive programming championship',
      date: '2025-01-21',
      time: '10:00 AM',
      venue: 'Computer Lab A',
      participants: 45,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'upcoming',
      color: 'from-blue-500 to-cyan-600',
      difficulty: 'Advanced',
      duration: '4 hours'
    },
    {
      id: 2,
      name: 'IHPC (Inter-Hostel Programming Contest)',
      category: 'Programming',
      icon: Cpu,
      description: 'Team-based algorithmic problem solving',
      date: '2025-01-22',
      time: '09:00 AM',
      venue: 'Computer Lab B',
      participants: 60,
      points: { gold: 150, silver: 100, bronze: 75 },
      status: 'upcoming',
      color: 'from-purple-500 to-indigo-600',
      difficulty: 'Expert',
      duration: '5 hours'
    },
    {
      id: 3,
      name: 'Hackathon',
      category: 'Development',
      icon: Lightbulb,
      description: '24-hour innovation challenge',
      date: '2025-01-23',
      time: '12:00 PM',
      venue: 'Innovation Hub',
      participants: 32,
      points: { gold: 200, silver: 150, bronze: 100 },
      status: 'upcoming',
      color: 'from-green-500 to-emerald-600',
      difficulty: 'All Levels',
      duration: '24 hours'
    },
    {
      id: 4,
      name: 'Web Development Contest',
      category: 'Web Dev',
      icon: Zap,
      description: 'Build stunning web applications',
      date: '2025-01-20',
      time: '02:00 PM',
      venue: 'Computer Lab C',
      participants: 28,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'ongoing',
      color: 'from-yellow-500 to-orange-600',
      difficulty: 'Intermediate',
      duration: '6 hours'
    },
    {
      id: 5,
      name: 'Game Development',
      category: 'Game Dev',
      icon: Gamepad2,
      description: 'Create engaging games and interactive experiences',
      date: '2025-01-24',
      time: '11:00 AM',
      venue: 'Game Dev Studio',
      participants: 20,
      points: { gold: 125, silver: 100, bronze: 75 },
      status: 'upcoming',
      color: 'from-red-500 to-pink-600',
      difficulty: 'Advanced',
      duration: '8 hours'
    },
    {
      id: 6,
      name: 'Data Science Challenge',
      category: 'Data Science',
      icon: Cpu,
      description: 'Analyze data and build predictive models',
      date: '2025-01-19',
      time: '03:00 PM',
      venue: 'Data Lab',
      participants: 25,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'completed',
      color: 'from-teal-500 to-cyan-600',
      difficulty: 'Advanced',
      duration: '3 hours'
    },
    {
      id: 7,
      name: 'Cybersecurity CTF',
      category: 'Security',
      icon: Zap,
      description: 'Capture the flag security challenges',
      date: '2025-01-25',
      time: '01:00 PM',
      venue: 'Security Lab',
      participants: 18,
      points: { gold: 100, silver: 75, bronze: 50 },
      status: 'upcoming',
      color: 'from-indigo-500 to-purple-600',
      difficulty: 'Expert',
      duration: '4 hours'
    },
    {
      id: 8,
      name: 'AI/ML Showcase',
      category: 'AI/ML',
      icon: Cpu,
      description: 'Demonstrate your machine learning projects',
      date: '2025-01-26',
      time: '10:30 AM',
      venue: 'AI Lab',
      participants: 15,
      points: { gold: 125, silver: 100, bronze: 75 },
      status: 'upcoming',
      color: 'from-violet-500 to-purple-600',
      difficulty: 'Advanced',
      duration: '2 hours'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing':
        return 'bg-green-500 animate-pulse';
      case 'upcoming':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'Intermediate':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'Advanced':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'Expert':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
    }
  };

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
            Technical Events
          </h1>
          <p className="text-xl text-gray-300">Code, innovate, and showcase your technical prowess for maximum points</p>
        </motion.div>

        {/* Enhanced Featured Hackathon */}
        <motion.div
          className="mb-16 relative overflow-hidden rounded-3xl bg-gradient-to-r from-green-500/20 to-emerald-600/20 backdrop-blur-sm border border-green-500/30"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-600/10"></div>
          <div className="relative z-10 p-12 text-center">
            <div className="text-7xl mb-6">🚀</div>
            <h2 className="text-5xl font-bold text-white mb-6">24-Hour Hackathon</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              The ultimate coding marathon! Build innovative solutions, collaborate with brilliant minds, 
              and compete for the highest points in technical events.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center text-gray-300 mb-8">
              <div className="flex items-center space-x-2">
                <Calendar className="w-6 h-6 text-green-400" />
                <span className="text-lg">January 23-24, 2025</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-6 h-6 text-green-400" />
                <span className="text-lg">24 Hours Non-stop</span>
              </div>
              <div className="flex items-center space-x-2">
                <Trophy className="w-6 h-6 text-green-400" />
                <span className="text-lg">200 Points for Winner</span>
              </div>
            </div>
            <button className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-4 px-10 rounded-full transition-all duration-300 hover:scale-105 shadow-2xl">
              Register Now
            </button>
          </div>
        </motion.div>

        {/* Enhanced Events Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {technicalEvents.map((event, index) => (
            <motion.div
              key={event.id}
              className="group relative bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:border-white/30 transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              whileHover={{ scale: 1.02, y: -10 }}
            >
              {/* Enhanced Status Badge */}
              <div className={`absolute top-6 right-6 px-4 py-2 ${getStatusColor(event.status)} rounded-full text-xs font-medium text-white shadow-lg`}>
                {event.status.toUpperCase()}
              </div>

              {/* Glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${event.color}/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>

              <div className="mb-6 relative z-10">
                <div className="flex items-center space-x-4 mb-4">
                  <div className={`p-3 bg-gradient-to-r ${event.color} rounded-xl shadow-lg`}>
                    <event.icon className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">{event.name}</h3>
                    <p className="text-sm text-gray-400">{event.category}</p>
                  </div>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">{event.description}</p>
              </div>

              {/* Enhanced Difficulty Badge */}
              <div className={`inline-block px-4 py-2 rounded-full text-xs font-medium border mb-6 ${getDifficultyColor(event.difficulty)}`}>
                {event.difficulty}
              </div>

              <div className="space-y-3 mb-6 relative z-10">
                <div className="flex items-center space-x-3 text-gray-300">
                  <Calendar className="w-5 h-5 text-cyan-400" />
                  <span className="text-sm">{event.date} at {event.time}</span>
                </div>
                
                <div className="flex items-center space-x-3 text-gray-300">
                  <MapPin className="w-5 h-5 text-cyan-400" />
                  <span className="text-sm">{event.venue}</span>
                </div>
                
                <div className="flex items-center space-x-3 text-gray-300">
                  <Users className="w-5 h-5 text-cyan-400" />
                  <span className="text-sm">{event.participants} participants</span>
                </div>

                <div className="flex items-center space-x-3 text-gray-300">
                  <Clock className="w-5 h-5 text-cyan-400" />
                  <span className="text-sm">{event.duration}</span>
                </div>
              </div>

              {/* Points breakdown */}
              <div className="mb-6 pt-4 border-t border-white/10 relative z-10">
                <div className="text-sm text-gray-400 mb-3">Points Distribution</div>
                <div className="flex justify-between text-sm">
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-white font-medium">{event.points.gold}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span className="text-white font-medium">{event.points.silver}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                    <span className="text-white font-medium">{event.points.bronze}</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 relative z-10">
                <button className={`flex-1 bg-gradient-to-r ${event.color} hover:scale-105 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg`}>
                  View Details
                </button>
                <button className="flex-1 bg-white/10 hover:bg-white/20 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 text-sm border border-white/20">
                  Register
                </button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Tech Stack Categories */}
        <motion.div
          className="mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
            Tech Domains
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { name: 'Programming', count: 12, color: 'from-blue-500 to-cyan-600', icon: '💻' },
              { name: 'Web Dev', count: 8, color: 'from-green-500 to-emerald-600', icon: '🌐' },
              { name: 'AI/ML', count: 6, color: 'from-purple-500 to-indigo-600', icon: '🤖' },
              { name: 'Security', count: 4, color: 'from-red-500 to-pink-600', icon: '🔒' },
            ].map((domain, index) => (
              <motion.div
                key={domain.name}
                className={`p-8 bg-gradient-to-br ${domain.color}/10 rounded-3xl backdrop-blur-sm border border-white/10 text-center hover:border-white/20 transition-all duration-300 group hover:scale-105`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <div className="text-5xl mb-4">{domain.icon}</div>
                <h3 className="text-white font-bold text-xl mb-2">{domain.name}</h3>
                <p className="text-gray-400">{domain.count} events</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Stats */}
        <motion.div
          className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="p-8 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-3xl backdrop-blur-sm border border-cyan-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Code className="w-12 h-12 mx-auto text-cyan-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">30+</div>
            <div className="text-gray-400">Technical Events</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-3xl backdrop-blur-sm border border-green-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Star className="w-12 h-12 mx-auto text-green-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-green-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">1000+</div>
            <div className="text-gray-400">Total Points Available</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-3xl backdrop-blur-sm border border-purple-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Users className="w-12 h-12 mx-auto text-purple-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">500+</div>
            <div className="text-gray-400">Expected Participants</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Technical;