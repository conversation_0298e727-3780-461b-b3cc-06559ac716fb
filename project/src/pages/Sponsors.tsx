import React from 'react';
import { motion } from 'framer-motion';
import { Star, Crown, Award, Medal, ExternalLink, Download, Mail, Phone, Users, Calendar, Zap } from 'lucide-react';

const Sponsors = () => {
  const sponsors = {
    platinum: [
      {
        id: 1,
        name: 'TechCorp Industries',
        logo: '🏢',
        description: 'Leading technology solutions provider',
        website: 'https://techcorp.com',
        benefits: ['Title Sponsor', 'Main Stage Branding', 'Opening Ceremony Slot', 'Recruitment Drive Access']
      },
      {
        id: 2,
        name: 'InnovateLabs',
        logo: '🚀',
        description: 'Innovation and startup incubator',
        website: 'https://innovatelabs.com',
        benefits: ['Hackathon Title Sponsor', 'Recruitment Drive', 'Mentorship Program', 'Workshop Hosting']
      }
    ],
    gold: [
      {
        id: 3,
        name: 'DataFlow Systems',
        logo: '💾',
        description: 'Big data and analytics solutions',
        website: 'https://dataflow.com',
        benefits: ['Technical Events Sponsor', 'Workshop Hosting', 'Student Interaction', 'Brand Visibility']
      },
      {
        id: 4,
        name: 'CloudTech Solutions',
        logo: '☁️',
        description: 'Cloud infrastructure and services',
        website: 'https://cloudtech.com',
        benefits: ['Infrastructure Support', 'Live Streaming', 'Digital Platform', 'Tech Support']
      },
      {
        id: 5,
        name: 'SportsPro Equipment',
        logo: '⚽',
        description: 'Premium sports equipment manufacturer',
        website: 'https://sportspro.com',
        benefits: ['Sports Events Sponsor', 'Equipment Supply', 'Athletic Gear', 'Sports Promotion']
      }
    ],
    silver: [
      {
        id: 6,
        name: 'FoodieHub',
        logo: '🍕',
        description: 'Food delivery and catering services',
        website: 'https://foodiehub.com',
        benefits: ['Food Court Sponsor', 'Catering Services', 'Late Night Snacks', 'Student Engagement']
      },
      {
        id: 7,
        name: 'MediaMax Productions',
        logo: '🎬',
        description: 'Event photography and videography',
        website: 'https://mediamax.com',
        benefits: ['Photography Coverage', 'Video Production', 'Live Streaming', 'Content Creation']
      },
      {
        id: 8,
        name: 'PrintCraft',
        logo: '🖨️',
        description: 'Printing and promotional materials',
        website: 'https://printcraft.com',
        benefits: ['Printing Services', 'Banners & Posters', 'Merchandise', 'Marketing Materials']
      },
      {
        id: 9,
        name: 'EcoGreen Solutions',
        logo: '🌱',
        description: 'Sustainable and eco-friendly products',
        website: 'https://ecogreen.com',
        benefits: ['Sustainability Partner', 'Eco-friendly Supplies', 'Green Initiatives', 'Environmental Awareness']
      }
    ]
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return 'from-purple-500 to-indigo-600';
      case 'gold':
        return 'from-yellow-400 to-orange-500';
      case 'silver':
        return 'from-gray-300 to-gray-500';
      default:
        return 'from-blue-400 to-blue-600';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return <Crown className="w-6 h-6" />;
      case 'gold':
        return <Star className="w-6 h-6" />;
      case 'silver':
        return <Medal className="w-6 h-6" />;
      default:
        return <Award className="w-6 h-6" />;
    }
  };

  const sponsorshipTiers = [
    {
      tier: 'Platinum',
      color: 'from-purple-500 to-indigo-600',
      benefits: [
        'Title Sponsorship Rights',
        'Main Stage Branding',
        'Opening/Closing Ceremony Slots',
        'Recruitment Drive Access',
        'Workshop Hosting Rights',
        'Premium Booth Space',
        'Social Media Promotion',
        'Logo on All Materials',
        'Student Database Access',
        'VIP Hospitality'
      ]
    },
    {
      tier: 'Gold',
      color: 'from-yellow-400 to-orange-500',
      benefits: [
        'Event Category Sponsorship',
        'Stage Branding',
        'Booth Space',
        'Social Media Mentions',
        'Logo on Event Materials',
        'Networking Opportunities',
        'Student Interaction',
        'Brand Visibility',
        'Workshop Opportunities'
      ]
    },
    {
      tier: 'Silver',
      color: 'from-gray-300 to-gray-500',
      benefits: [
        'Specific Event Sponsorship',
        'Banner Display',
        'Booth Space',
        'Social Media Tags',
        'Logo on Select Materials',
        'Networking Access',
        'Student Interaction',
        'Brand Visibility'
      ]
    }
  ];

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            Our Sponsors
          </h1>
          <p className="text-xl text-gray-300">Powering Zephyr 2025 with innovation and support</p>
        </motion.div>

        {/* Enhanced Platinum Sponsors */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <Crown className="w-10 h-10 text-purple-400" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-indigo-500 bg-clip-text text-transparent">
                Platinum Sponsors
              </h2>
              <Crown className="w-10 h-10 text-purple-400" />
            </div>
            <p className="text-gray-300 text-lg">Our premier partners making Zephyr 2025 possible</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            {sponsors.platinum.map((sponsor, index) => (
              <motion.div
                key={sponsor.id}
                className="group relative bg-gradient-to-br from-purple-500/20 to-indigo-600/20 backdrop-blur-sm border border-purple-500/30 rounded-3xl p-10 hover:border-purple-500/50 transition-all duration-300 overflow-hidden"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                whileHover={{ scale: 1.02, y: -10 }}
              >
                <div className="absolute -top-6 -right-6 w-20 h-20 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center border-4 border-white/20 shadow-2xl">
                  <Crown className="w-10 h-10 text-white" />
                </div>
                
                {/* Glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-indigo-600/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div className="text-center mb-8 relative z-10">
                  <div className="text-7xl mb-6">{sponsor.logo}</div>
                  <h3 className="text-3xl font-bold text-white mb-3">{sponsor.name}</h3>
                  <p className="text-gray-300 text-lg">{sponsor.description}</p>
                </div>
                
                <div className="mb-8 relative z-10">
                  <div className="space-y-3">
                    <h4 className="text-white font-medium mb-4 text-lg">Partnership Benefits:</h4>
                    {sponsor.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-center space-x-3 text-gray-300">
                        <Star className="w-5 h-5 text-purple-400" />
                        <span>{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="relative z-10">
                  <a
                    href={sponsor.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-3 shadow-lg"
                  >
                    <ExternalLink className="w-5 h-5" />
                    <span>Visit Website</span>
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Gold Sponsors */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <Star className="w-8 h-8 text-yellow-400" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Gold Sponsors
              </h2>
              <Star className="w-8 h-8 text-yellow-400" />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {sponsors.gold.map((sponsor, index) => (
              <motion.div
                key={sponsor.id}
                className="group relative bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-sm border border-yellow-500/30 rounded-3xl p-8 hover:border-yellow-500/50 transition-all duration-300 overflow-hidden"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center border-4 border-white/20 shadow-xl">
                  <Star className="w-6 h-6 text-white" />
                </div>
                
                {/* Glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-600/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div className="text-center mb-6 relative z-10">
                  <div className="text-5xl mb-4">{sponsor.logo}</div>
                  <h3 className="text-xl font-bold text-white mb-2">{sponsor.name}</h3>
                  <p className="text-gray-300 text-sm">{sponsor.description}</p>
                </div>
                
                <div className="mb-6 relative z-10">
                  <div className="space-y-2">
                    {sponsor.benefits.slice(0, 4).map((benefit, idx) => (
                      <div key={idx} className="flex items-center space-x-2 text-gray-300">
                        <Star className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="relative z-10">
                  <a
                    href={sponsor.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 text-sm shadow-lg"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Visit</span>
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Silver Sponsors */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <Medal className="w-7 h-7 text-gray-400" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-gray-300 to-gray-500 bg-clip-text text-transparent">
                Silver Sponsors
              </h2>
              <Medal className="w-7 h-7 text-gray-400" />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {sponsors.silver.map((sponsor, index) => (
              <motion.div
                key={sponsor.id}
                className="group relative bg-gradient-to-br from-gray-500/20 to-gray-600/20 backdrop-blur-sm border border-gray-500/30 rounded-2xl p-6 hover:border-gray-500/50 transition-all duration-300 overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                whileHover={{ scale: 1.02 }}
              >
                {/* Glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-gray-500/10 to-gray-600/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div className="text-center mb-4 relative z-10">
                  <div className="text-4xl mb-3">{sponsor.logo}</div>
                  <h3 className="text-lg font-bold text-white mb-1">{sponsor.name}</h3>
                  <p className="text-gray-300 text-xs">{sponsor.description}</p>
                </div>
                
                <div className="relative z-10">
                  <a
                    href={sponsor.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-medium py-2 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 text-xs shadow-lg"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span>Visit</span>
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Sponsorship Opportunities */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="text-center mb-16">
            <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
              Become a Sponsor
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto">
              Join us in creating an unforgettable experience for 3000+ students. 
              Partner with BITS Goa's premier inter-hostel festival and showcase your brand to the brightest minds.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-16">
            {sponsorshipTiers.map((tier, index) => (
              <motion.div
                key={tier.tier}
                className={`p-10 bg-gradient-to-br ${tier.color}/10 backdrop-blur-sm border border-white/10 rounded-3xl hover:border-white/20 transition-all duration-300 group hover:scale-105`}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
              >
                <div className="text-center mb-8">
                  <div className={`inline-block p-6 bg-gradient-to-r ${tier.color} rounded-full mb-6 shadow-2xl`}>
                    {getTierIcon(tier.tier.toLowerCase())}
                  </div>
                  <h3 className={`text-3xl font-bold mb-4 bg-gradient-to-r ${tier.color} bg-clip-text text-transparent`}>
                    {tier.tier}
                  </h3>
                </div>
                
                <div className="space-y-4">
                  {tier.benefits.map((benefit, idx) => (
                    <div key={idx} className="flex items-center space-x-3 text-gray-300">
                      <Star className="w-5 h-5 text-cyan-400" />
                      <span>{benefit}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
          
          <div className="text-center">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto mb-12">
              <button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-5 px-10 rounded-full transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-3 shadow-2xl">
                <Download className="w-6 h-6" />
                <span>Download Prospectus</span>
              </button>
              
              <button className="bg-gradient-to-r from-fuchsia-500 to-purple-600 hover:from-fuchsia-600 hover:to-purple-700 text-white font-bold py-5 px-10 rounded-full transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-3 shadow-2xl">
                <Mail className="w-6 h-6" />
                <span>Contact Sponsorship Team</span>
              </button>
            </div>
            
            <div className="p-8 bg-black/20 backdrop-blur-sm rounded-3xl border border-white/10">
              <h3 className="text-2xl font-bold text-white mb-6">Get in Touch</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-300">
                <div className="flex items-center justify-center space-x-3">
                  <Mail className="w-6 h-6 text-cyan-400" />
                  <span className="text-lg"><EMAIL></span>
                </div>
                <div className="flex items-center justify-center space-x-3">
                  <Phone className="w-6 h-6 text-cyan-400" />
                  <span className="text-lg">+91-9876543210</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Enhanced Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="p-8 bg-gradient-to-br from-purple-500/10 to-indigo-500/10 rounded-3xl backdrop-blur-sm border border-purple-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Crown className="w-12 h-12 mx-auto text-purple-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">15+</div>
            <div className="text-gray-400">Partner Companies</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-3xl backdrop-blur-sm border border-cyan-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Users className="w-12 h-12 mx-auto text-cyan-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">3000+</div>
            <div className="text-gray-400">Student Reach</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-3xl backdrop-blur-sm border border-green-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Calendar className="w-12 h-12 mx-auto text-green-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-green-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">5</div>
            <div className="text-gray-400">Days of Exposure</div>
          </div>
          
          <div className="p-8 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl backdrop-blur-sm border border-yellow-500/20 text-center group hover:scale-105 transition-all duration-300">
            <div className="relative mb-6">
              <Zap className="w-12 h-12 mx-auto text-yellow-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-yellow-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-3xl font-bold text-white mb-2">30+</div>
            <div className="text-gray-400">Event Categories</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Sponsors;