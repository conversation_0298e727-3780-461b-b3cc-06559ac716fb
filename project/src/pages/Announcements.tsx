import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Megaphone, Clock, Pin, AlertTriangle, Info, CheckCircle, Calendar, User, Filter } from 'lucide-react';

const Announcements = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');

  const announcements = [
    {
      id: 1,
      title: 'Football Semi-Final Schedule Updated',
      content: 'Due to weather conditions, the football semi-final between DH1 and AH3 has been rescheduled to tomorrow at 10:00 AM. Please check the fixtures page for updated timings.',
      category: 'sports',
      priority: 'high',
      author: 'Sports Committee',
      timestamp: '2025-01-20T14:30:00Z',
      pinned: true,
      tags: ['Football', 'Schedule Change', 'Weather']
    },
    {
      id: 2,
      title: 'Cultural Night Registration Extended',
      content: 'Registration for the Grand Cultural Night has been extended until January 22nd, 11:59 PM. Don\'t miss your chance to showcase your talent on the biggest stage of Zephyr 2025!',
      category: 'cultural',
      priority: 'medium',
      author: 'Cultural Committee',
      timestamp: '2025-01-20T12:15:00Z',
      pinned: true,
      tags: ['Cultural', 'Registration', 'Deadline']
    },
    {
      id: 3,
      title: 'Hackathon Venue Changed',
      content: 'The 24-hour Hackathon venue has been moved from Innovation Hub to the Main Computer Lab due to increased registrations. All participants will receive updated venue details via email.',
      category: 'technical',
      priority: 'high',
      author: 'Technical Committee',
      timestamp: '2025-01-20T10:45:00Z',
      pinned: false,
      tags: ['Hackathon', 'Venue Change', 'Technical']
    },
    {
      id: 4,
      title: 'Mess Timings During Zephyr',
      content: 'Special mess timings are in effect during Zephyr 2025. Breakfast: 7:00-10:00 AM, Lunch: 12:00-3:00 PM, Dinner: 7:00-10:00 PM. Late night snacks available at the food court.',
      category: 'general',
      priority: 'low',
      author: 'Mess Committee',
      timestamp: '2025-01-20T09:00:00Z',
      pinned: false,
      tags: ['Mess', 'Timings', 'Food']
    },
    {
      id: 5,
      title: 'Photography Contest Submission Guidelines',
      content: 'All photography contest submissions must be in RAW or high-quality JPEG format (minimum 300 DPI). Submissions can be made through the official portal until January 21st, 6:00 PM.',
      category: 'cultural',
      priority: 'medium',
      author: 'Photography Club',
      timestamp: '2025-01-19T16:20:00Z',
      pinned: false,
      tags: ['Photography', 'Submission', 'Guidelines']
    },
    {
      id: 6,
      title: 'Emergency Contact Information',
      content: 'For any emergencies during Zephyr events, contact the 24/7 helpline: +91-**********. Medical assistance is available at the campus health center throughout the festival.',
      category: 'general',
      priority: 'high',
      author: 'Safety Committee',
      timestamp: '2025-01-19T14:00:00Z',
      pinned: true,
      tags: ['Emergency', 'Safety', 'Contact']
    },
    {
      id: 7,
      title: 'Sponsor Interaction Session',
      content: 'Meet our platinum sponsors at the networking session on January 22nd, 4:00 PM at the Main Auditorium. Great opportunity for internships and job placements!',
      category: 'general',
      priority: 'medium',
      author: 'Sponsorship Team',
      timestamp: '2025-01-19T11:30:00Z',
      pinned: false,
      tags: ['Sponsors', 'Networking', 'Careers']
    },
    {
      id: 8,
      title: 'Live Streaming Links Available',
      content: 'Can\'t make it to the venue? Watch all major events live on our official YouTube channel and Instagram. Links are available on the Live Updates page.',
      category: 'general',
      priority: 'low',
      author: 'Media Team',
      timestamp: '2025-01-19T08:45:00Z',
      pinned: false,
      tags: ['Live Stream', 'YouTube', 'Instagram']
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'from-red-500 to-pink-600';
      case 'medium':
        return 'from-yellow-500 to-orange-600';
      case 'low':
        return 'from-green-500 to-emerald-600';
      default:
        return 'from-blue-500 to-cyan-600';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className="w-4 h-4" />;
      case 'medium':
        return <Info className="w-4 h-4" />;
      case 'low':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'sports':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'cultural':
        return 'bg-pink-500/20 text-pink-400 border-pink-500/30';
      case 'technical':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'general':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default:
        return 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const filteredAnnouncements = announcements.filter(announcement => {
    const categoryMatch = selectedCategory === 'all' || announcement.category === selectedCategory;
    const priorityMatch = selectedPriority === 'all' || announcement.priority === selectedPriority;
    return categoryMatch && priorityMatch;
  });

  const pinnedAnnouncements = filteredAnnouncements.filter(a => a.pinned);
  const regularAnnouncements = filteredAnnouncements.filter(a => !a.pinned);

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Announcements
          </h1>
          <p className="text-xl text-gray-300">Stay updated with the latest news and updates</p>
        </motion.div>

        {/* Filters */}
        <motion.div
          className="mb-8 p-6 bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center space-x-2 mb-4">
            <Filter className="w-5 h-5 text-cyan-400" />
            <span className="text-white font-medium">Filters:</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
              >
                <option value="all" className="bg-gray-800">All Categories</option>
                <option value="sports" className="bg-gray-800">Sports</option>
                <option value="cultural" className="bg-gray-800">Cultural</option>
                <option value="technical" className="bg-gray-800">Technical</option>
                <option value="general" className="bg-gray-800">General</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Priority</label>
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
              >
                <option value="all" className="bg-gray-800">All Priorities</option>
                <option value="high" className="bg-gray-800">High Priority</option>
                <option value="medium" className="bg-gray-800">Medium Priority</option>
                <option value="low" className="bg-gray-800">Low Priority</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Pinned Announcements */}
        {pinnedAnnouncements.length > 0 && (
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="flex items-center space-x-2 mb-6">
              <Pin className="w-5 h-5 text-yellow-400" />
              <h2 className="text-2xl font-bold text-white">Pinned Announcements</h2>
            </div>
            
            <div className="space-y-4">
              {pinnedAnnouncements.map((announcement, index) => (
                <motion.div
                  key={announcement.id}
                  className="group relative bg-gradient-to-br from-yellow-500/10 to-orange-500/10 backdrop-blur-sm border border-yellow-500/30 rounded-2xl p-6 hover:border-yellow-500/50 transition-all duration-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  whileHover={{ scale: 1.01 }}
                >
                  <div className="absolute top-4 right-4">
                    <Pin className="w-5 h-5 text-yellow-400" />
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-xl font-bold text-white pr-8">{announcement.title}</h3>
                    </div>
                    
                    <div className="flex items-center space-x-3 mb-3">
                      <div className={`px-3 py-1 bg-gradient-to-r ${getPriorityColor(announcement.priority)} rounded-full text-xs font-medium text-white flex items-center space-x-1`}>
                        {getPriorityIcon(announcement.priority)}
                        <span>{announcement.priority.toUpperCase()}</span>
                      </div>
                      
                      <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getCategoryColor(announcement.category)}`}>
                        {announcement.category.toUpperCase()}
                      </div>
                    </div>
                    
                    <p className="text-gray-300 leading-relaxed">{announcement.content}</p>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-400">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{announcement.author}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{formatTimestamp(announcement.timestamp)}</span>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1">
                      {announcement.tags.map(tag => (
                        <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Regular Announcements */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="flex items-center space-x-2 mb-6">
            <Megaphone className="w-5 h-5 text-cyan-400" />
            <h2 className="text-2xl font-bold text-white">Recent Announcements</h2>
          </div>
          
          <div className="space-y-4">
            {regularAnnouncements.map((announcement, index) => (
              <motion.div
                key={announcement.id}
                className="group relative bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:border-white/20 transition-all duration-300"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                whileHover={{ scale: 1.01 }}
              >
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-white mb-3">{announcement.title}</h3>
                  
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`px-3 py-1 bg-gradient-to-r ${getPriorityColor(announcement.priority)} rounded-full text-xs font-medium text-white flex items-center space-x-1`}>
                      {getPriorityIcon(announcement.priority)}
                      <span>{announcement.priority.toUpperCase()}</span>
                    </div>
                    
                    <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getCategoryColor(announcement.category)}`}>
                      {announcement.category.toUpperCase()}
                    </div>
                  </div>
                  
                  <p className="text-gray-300 leading-relaxed">{announcement.content}</p>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>{announcement.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatTimestamp(announcement.timestamp)}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {announcement.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* No announcements found */}
        {filteredAnnouncements.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <div className="text-6xl mb-4">📢</div>
            <h3 className="text-2xl font-bold text-white mb-2">No announcements found</h3>
            <p className="text-gray-400">Try adjusting your filters to see more announcements</p>
          </motion.div>
        )}

        {/* Quick Stats */}
        <motion.div
          className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="p-6 bg-gradient-to-br from-red-500/10 to-pink-500/10 rounded-2xl backdrop-blur-sm border border-red-500/20 text-center">
            <AlertTriangle className="w-8 h-8 mx-auto mb-3 text-red-400" />
            <div className="text-2xl font-bold text-white mb-1">
              {announcements.filter(a => a.priority === 'high').length}
            </div>
            <div className="text-gray-400">High Priority</div>
          </div>
          
          <div className="p-6 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-2xl backdrop-blur-sm border border-yellow-500/20 text-center">
            <Pin className="w-8 h-8 mx-auto mb-3 text-yellow-400" />
            <div className="text-2xl font-bold text-white mb-1">
              {announcements.filter(a => a.pinned).length}
            </div>
            <div className="text-gray-400">Pinned</div>
          </div>
          
          <div className="p-6 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-2xl backdrop-blur-sm border border-blue-500/20 text-center">
            <Calendar className="w-8 h-8 mx-auto mb-3 text-blue-400" />
            <div className="text-2xl font-bold text-white mb-1">
              {announcements.filter(a => {
                const today = new Date();
                const announcementDate = new Date(a.timestamp);
                return announcementDate.toDateString() === today.toDateString();
              }).length}
            </div>
            <div className="text-gray-400">Today</div>
          </div>
          
          <div className="p-6 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-2xl backdrop-blur-sm border border-green-500/20 text-center">
            <Megaphone className="w-8 h-8 mx-auto mb-3 text-green-400" />
            <div className="text-2xl font-bold text-white mb-1">{announcements.length}</div>
            <div className="text-gray-400">Total</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Announcements;