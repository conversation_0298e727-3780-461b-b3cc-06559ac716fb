import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, MapPin, Users, Filter, ChevronDown, Trophy, Star } from 'lucide-react';

const Fixtures = () => {
  const [selectedDate, setSelectedDate] = useState('all');
  const [selectedSport, setSelectedSport] = useState('all');
  const [selectedHostel, setSelectedHostel] = useState('all');
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'calendar'

  const fixtures = [
    {
      id: 1,
      sport: 'Football',
      team1: 'DH1',
      team2: 'AH3',
      date: '2025-01-20',
      time: '09:00 AM',
      venue: 'Main Ground',
      status: 'scheduled',
      round: 'Quarter Final',
      tier: 'gold'
    },
    {
      id: 2,
      sport: 'Cricket',
      team1: 'CH6',
      team2: 'DH4',
      date: '2025-01-20',
      time: '02:00 PM',
      venue: 'Cricket Ground',
      status: 'live',
      round: 'Semi Final',
      tier: 'gold'
    },
    {
      id: 3,
      sport: 'Basketball',
      team1: 'AH1',
      team2: 'CH7',
      date: '2025-01-20',
      time: '04:00 PM',
      venue: 'Basketball Court',
      status: 'completed',
      round: 'Final',
      tier: 'silver',
      score: '78-65'
    },
    {
      id: 4,
      sport: 'Football',
      team1: 'DH2',
      team2: 'AH5',
      date: '2025-01-21',
      time: '10:00 AM',
      venue: 'Main Ground',
      status: 'scheduled',
      round: 'Quarter Final',
      tier: 'gold'
    },
    {
      id: 5,
      sport: 'Volleyball',
      team1: 'CH5',
      team2: 'DH3',
      date: '2025-01-21',
      time: '03:00 PM',
      venue: 'Volleyball Court',
      status: 'scheduled',
      round: 'Semi Final',
      tier: 'silver'
    },
    {
      id: 6,
      sport: 'Table Tennis',
      team1: 'AH2',
      team2: 'CH4',
      date: '2025-01-21',
      time: '11:00 AM',
      venue: 'TT Hall',
      status: 'scheduled',
      round: 'Quarter Final',
      tier: 'silver'
    },
    {
      id: 7,
      sport: 'Badminton',
      team1: 'DH5',
      team2: 'AH4',
      date: '2025-01-22',
      time: '09:30 AM',
      venue: 'Sports Complex',
      status: 'scheduled',
      round: 'Final',
      tier: 'silver'
    },
    {
      id: 8,
      sport: 'Tug of War',
      team1: 'All Hostels',
      team2: '',
      date: '2025-01-23',
      time: '05:00 PM',
      venue: 'Central Ground',
      status: 'scheduled',
      round: 'Grand Finale',
      tier: 'platinum'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'live':
        return 'bg-red-500 animate-pulse';
      case 'scheduled':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return 'from-purple-500 to-indigo-600';
      case 'gold':
        return 'from-yellow-400 to-orange-500';
      case 'silver':
        return 'from-gray-300 to-gray-500';
      default:
        return 'from-blue-400 to-blue-600';
    }
  };

  const filteredFixtures = fixtures.filter(fixture => {
    const dateMatch = selectedDate === 'all' || fixture.date === selectedDate;
    const sportMatch = selectedSport === 'all' || fixture.sport === selectedSport;
    const hostelMatch = selectedHostel === 'all' || 
      fixture.team1.includes(selectedHostel) || 
      fixture.team2.includes(selectedHostel);
    
    return dateMatch && sportMatch && hostelMatch;
  });

  const uniqueDates = [...new Set(fixtures.map(f => f.date))];
  const uniqueSports = [...new Set(fixtures.map(f => f.sport))];
  const hostels = ['AH1', 'AH2', 'AH3', 'AH4', 'AH5', 'AH6', 'AH7', 'AH8', 'AH9', 'DH1', 'DH2', 'DH3', 'DH4', 'CH1', 'CH4', 'CH5', 'CH6', 'CH7'];

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Fixtures & Results
          </h1>
          <p className="text-xl text-gray-300">Complete schedule and match results</p>
        </motion.div>

        {/* Filters */}
        <motion.div
          className="mb-8 p-6 bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center space-x-2 mb-4">
            <Filter className="w-5 h-5 text-cyan-400" />
            <span className="text-white font-medium">Filters:</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date</label>
              <select
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
              >
                <option value="all">All Dates</option>
                {uniqueDates.map(date => (
                  <option key={date} value={date} className="bg-gray-800">
                    {new Date(date).toLocaleDateString()}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Sport</label>
              <select
                value={selectedSport}
                onChange={(e) => setSelectedSport(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
              >
                <option value="all">All Sports</option>
                {uniqueSports.map(sport => (
                  <option key={sport} value={sport} className="bg-gray-800">
                    {sport}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Hostel</label>
              <select
                value={selectedHostel}
                onChange={(e) => setSelectedHostel(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
              >
                <option value="all">All Hostels</option>
                {hostels.map(hostel => (
                  <option key={hostel} value={hostel} className="bg-gray-800">
                    {hostel}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">View</label>
              <div className="flex space-x-2">
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                    viewMode === 'list' ? 'bg-cyan-500 text-white' : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  List
                </button>
                <button
                  onClick={() => setViewMode('calendar')}
                  className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                    viewMode === 'calendar' ? 'bg-cyan-500 text-white' : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  Calendar
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Live Matches Banner */}
        {fixtures.some(f => f.status === 'live') && (
          <motion.div
            className="mb-8 p-6 bg-gradient-to-r from-red-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl border border-red-500/30"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <h2 className="text-xl font-bold text-white">Live Matches</h2>
            </div>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              {fixtures.filter(f => f.status === 'live').map(match => (
                <div key={match.id} className="p-4 bg-black/20 rounded-lg border border-red-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-red-400">{match.sport} - {match.round}</span>
                    <span className="text-xs text-gray-400">{match.venue}</span>
                  </div>
                  <div className="text-white font-bold text-lg">
                    {match.team1} vs {match.team2}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Fixtures List */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {filteredFixtures.map((fixture, index) => (
            <motion.div
              key={fixture.id}
              className="group relative bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:border-white/20 transition-all duration-300"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              whileHover={{ scale: 1.01 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                {/* Match Info */}
                <div className="md:col-span-2">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`px-3 py-1 ${getStatusColor(fixture.status)} rounded-full text-xs font-medium text-white`}>
                      {fixture.status.toUpperCase()}
                    </div>
                    <div className={`px-3 py-1 bg-gradient-to-r ${getTierColor(fixture.tier)} rounded-full text-xs font-medium text-white`}>
                      {fixture.tier.toUpperCase()}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-bold text-white mb-1">{fixture.sport}</h3>
                  <p className="text-gray-400 text-sm">{fixture.round}</p>
                  
                  <div className="mt-3">
                    {fixture.team2 ? (
                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">{fixture.team1}</div>
                        </div>
                        <div className="text-gray-400 font-medium">VS</div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">{fixture.team2}</div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-lg font-bold text-white">{fixture.team1}</div>
                    )}
                    
                    {fixture.score && (
                      <div className="mt-2 text-center">
                        <span className="text-2xl font-bold text-cyan-400">{fixture.score}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Date & Time */}
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 text-gray-300 mb-2">
                    <Calendar className="w-4 h-4 text-cyan-400" />
                    <span className="text-sm">{new Date(fixture.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2 text-gray-300">
                    <Clock className="w-4 h-4 text-cyan-400" />
                    <span className="text-sm">{fixture.time}</span>
                  </div>
                </div>
                
                {/* Venue & Actions */}
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 text-gray-300 mb-4">
                    <MapPin className="w-4 h-4 text-cyan-400" />
                    <span className="text-sm">{fixture.venue}</span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 text-sm">
                      {fixture.status === 'live' ? 'Watch Live' : 'Details'}
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* No fixtures found */}
        {filteredFixtures.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <div className="text-6xl mb-4">📅</div>
            <h3 className="text-2xl font-bold text-white mb-2">No fixtures found</h3>
            <p className="text-gray-400">Try adjusting your filters to see more matches</p>
          </motion.div>
        )}

        {/* Upcoming Highlights */}
        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h2 className="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
            Upcoming Highlights
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {fixtures.filter(f => f.status === 'scheduled').slice(0, 3).map((fixture, index) => (
              <motion.div
                key={fixture.id}
                className={`p-6 bg-gradient-to-br ${getTierColor(fixture.tier)}/10 rounded-2xl backdrop-blur-sm border border-white/10 text-center hover:border-white/20 transition-all duration-300`}
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <div className="mb-4">
                  <div className={`inline-block px-3 py-1 bg-gradient-to-r ${getTierColor(fixture.tier)} rounded-full text-xs font-medium text-white mb-2`}>
                    {fixture.tier.toUpperCase()}
                  </div>
                  <h3 className="text-xl font-bold text-white">{fixture.sport}</h3>
                  <p className="text-gray-400 text-sm">{fixture.round}</p>
                </div>
                
                <div className="text-white font-medium mb-2">
                  {fixture.team1} {fixture.team2 && `vs ${fixture.team2}`}
                </div>
                
                <div className="text-sm text-gray-300">
                  {new Date(fixture.date).toLocaleDateString()} at {fixture.time}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Fixtures;