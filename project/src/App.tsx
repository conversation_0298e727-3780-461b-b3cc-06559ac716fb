import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import Leaderboard from './pages/Leaderboard';
import Fixtures from './pages/Fixtures';
import Live from './pages/Live';
import Announcements from './pages/Announcements';
import About from './pages/About';
import Footer from './components/Footer';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-x-hidden" style={{ overscrollBehavior: 'none', overscrollBehaviorY: 'none' }}>
        {/* Clean, performance-friendly background */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-orange-900/10 via-transparent to-yellow-900/10"></div>

          {/* Minimal grid pattern */}
          <div className="absolute inset-0 opacity-20 bg-[linear-gradient(rgba(251,146,60,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(251,146,60,0.02)_1px,transparent_1px)] bg-[size:100px_100px]"></div>
        </div>

        <Navbar />

        <motion.main
          className="relative z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/leaderboard" element={<Leaderboard />} />
            <Route path="/fixtures" element={<Fixtures />} />
            <Route path="/live" element={<Live />} />
            <Route path="/announcements" element={<Announcements />} />
            <Route path="/about" element={<About />} />
          </Routes>
        </motion.main>

        <Footer />
      </div>
    </Router>
  );
}

export default App;