@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional styling and overscroll fixes */
@layer base {
  html {
    overscroll-behavior: none;
    overscroll-behavior-y: none;
    scroll-behavior: smooth;
  }

  body {
    overscroll-behavior: none;
    overscroll-behavior-y: none;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variant-ligatures: common-ligatures;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    overscroll-behavior: none;
  }
}

/* Professional animations and transitions */
@layer components {
  .professional-card {
    @apply backdrop-blur-sm border border-white/10 rounded-3xl transition-all duration-300;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }

  .professional-card:hover {
    @apply border-white/20;
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.18),
      inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }

  .gradient-text-zephyr {
    @apply bg-gradient-to-r from-orange-400 via-red-400 to-yellow-500 bg-clip-text text-transparent;
  }

  .professional-button {
    @apply relative overflow-hidden rounded-2xl font-semibold transition-all duration-300;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .professional-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  }

  .professional-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transition: left 0.5s;
  }

  .professional-button:hover::before {
    left: 100%;
  }
}

/* Custom scrollbar */
@layer utilities {
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #f97316, #dc2626);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #ea580c, #b91c1c);
  }
}
