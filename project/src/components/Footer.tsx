import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Zap, Mail, Phone, MapPin, Instagram, Twitter, Youtube, Facebook } from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { name: 'Home', path: '/' },
    { name: 'Leaderboard', path: '/leaderboard' },
    { name: 'Fixtures & Results', path: '/fixtures' },
    { name: 'Live Updates', path: '/live' },
    { name: 'About', path: '/about' }
  ];

  const importantLinks = [
    { name: 'Fixtures', path: '/fixtures' },
    { name: 'Announcements', path: '/announcements' },
    { name: 'Sponsors', path: '/sponsors' },
    { name: 'About Us', path: '/about' },
    { name: 'Contact', path: '/about' },
    { name: 'Rules & Regulations', path: '#' }
  ];

  const socialLinks = [
    { name: 'Instagram', icon: Instagram, url: 'https://instagram.com/zephyr2025', color: 'hover:text-pink-400' },
    { name: 'Twitter', icon: Twitter, url: 'https://twitter.com/zephyr2025', color: 'hover:text-blue-400' },
    { name: 'YouTube', icon: Youtube, url: 'https://youtube.com/zephyr2025', color: 'hover:text-red-400' },
    { name: 'Facebook', icon: Facebook, url: 'https://facebook.com/zephyr2025', color: 'hover:text-blue-500' }
  ];

  const hostels = {
    boys: ['AH1', 'AH2', 'AH3', 'AH4', 'AH5', 'AH6', 'AH7', 'AH8', 'AH9', 'DH1', 'DH2', 'DH3', 'DH4', 'DH5', 'CH1'],
    girls: ['CH4', 'CH5', 'CH6', 'CH7']
  };

  return (
    <footer className="relative bg-black/40 backdrop-blur-sm border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Link to="/" className="flex items-center space-x-3 mb-6">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-fuchsia-500 rounded-xl flex items-center justify-center">
                  <Zap className="w-7 h-7 text-white" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-fuchsia-500 rounded-xl blur opacity-60 animate-pulse"></div>
              </div>
              <div>
                <div className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-fuchsia-500 bg-clip-text text-transparent">
                  Zephyr 2025
                </div>
                <div className="text-sm text-gray-400">BITS Goa Inter-Hostel Fest</div>
              </div>
            </Link>
            
            <p className="text-gray-300 mb-6 leading-relaxed">
              The premier inter-hostel festival celebrating sports, culture, and technical excellence. 
              Join 17 hostels in 5 days of unforgettable competition and camaraderie.
            </p>
            
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-400 transition-all duration-300 ${social.color} hover:bg-white/20`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold text-white mb-6">Quick Links</h3>
            <div className="space-y-3">
              {quickLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className="block text-gray-300 hover:text-cyan-400 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </motion.div>

          {/* Important Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold text-white mb-6">Important</h3>
            <div className="space-y-3">
              {importantLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className="block text-gray-300 hover:text-cyan-400 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </motion.div>

          {/* Contact & Hostels */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold text-white mb-6">Contact Info</h3>
            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3 text-gray-300">
                <Mail className="w-5 h-5 text-cyan-400" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <Phone className="w-5 h-5 text-cyan-400" />
                <span className="text-sm">+91-9876543210</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <MapPin className="w-5 h-5 text-cyan-400" />
                <span className="text-sm">BITS Pilani, Goa Campus</span>
              </div>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Participating Hostels</h4>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-blue-400 mb-2">Boys Hostels:</p>
                  <div className="flex flex-wrap gap-1">
                    {hostels.boys.map((hostel) => (
                      <span key={hostel} className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">
                        {hostel}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-pink-400 mb-2">Girls Hostels:</p>
                  <div className="flex flex-wrap gap-1">
                    {hostels.girls.map((hostel) => (
                      <span key={hostel} className="px-2 py-1 bg-pink-500/20 text-pink-400 rounded text-xs">
                        {hostel}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          className="mt-12 pt-8 border-t border-white/10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © 2025 Zephyr, BITS Pilani Goa Campus. All rights reserved.
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <Link to="#" className="hover:text-cyan-400 transition-colors">Privacy Policy</Link>
              <Link to="#" className="hover:text-cyan-400 transition-colors">Terms of Service</Link>
              <Link to="#" className="hover:text-cyan-400 transition-colors">Code of Conduct</Link>
            </div>
          </div>
          
          <div className="mt-6 text-center">
            <p className="text-gray-500 text-sm">
              Made with <span className="text-red-400">❤️</span> by the Zephyr 2025 Tech Team
            </p>
          </div>
        </motion.div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-cyan-500/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-32 -right-32 w-64 h-64 bg-fuchsia-500/5 rounded-full blur-3xl"></div>
      </div>
    </footer>
  );
};

export default Footer;