import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Home, Trophy, Calendar, Users, Megaphone, Star } from 'lucide-react';
import zephyrLogo from '../zephyr.jpg';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const navItems = [
    { name: 'Home', path: '/', icon: Home },
    { name: 'Leaderboard', path: '/leaderboard', icon: Trophy },
    { name: 'Fixtures & Results', path: '/fixtures', icon: Calendar },
    { name: 'Live Updates', path: '/live', icon: Users },
    { name: 'Announcements', path: '/announcements', icon: Megaphone },
    { name: 'About', path: '/about', icon: Star },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/10 backdrop-blur-2xl border-b border-orange-500/20">
      {/* Glowing top border */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-400/60 to-transparent"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Enhanced Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-400 via-red-500 to-yellow-500 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 overflow-hidden">
                <img
                  src={zephyrLogo}
                  alt="Zephyr Logo"
                  className="w-10 h-10 object-cover rounded-lg"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-500 to-yellow-500 rounded-xl blur-lg opacity-40 group-hover:opacity-60 transition-opacity duration-300"></div>
            </div>
            <div>
              <div className="text-2xl font-bold bg-gradient-to-r from-orange-400 via-red-400 to-yellow-500 bg-clip-text text-transparent">
                Zephyr 2025
              </div>
              <div className="text-xs text-gray-400 tracking-wider">INTER-HOSTEL FEST</div>
            </div>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              return (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 flex items-center space-x-2 group ${
                    isActive
                      ? 'text-orange-400 bg-orange-500/10 shadow-lg shadow-orange-500/20'
                      : 'text-gray-300 hover:text-white hover:bg-white/5'
                  }`}
                >
                  <Icon className={`w-4 h-4 transition-all duration-300 ${isActive ? 'text-orange-400' : 'group-hover:text-orange-400'}`} />
                  <span>{item.name}</span>
                  {isActive && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-orange-500/20 via-red-500/20 to-yellow-500/20 rounded-xl border border-orange-500/30"
                      layoutId="activeTab"
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                  {/* Hover glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 via-red-500/0 to-yellow-500/0 group-hover:from-orange-500/10 group-hover:via-red-500/10 group-hover:to-yellow-500/10 rounded-xl transition-all duration-300"></div>
                </Link>
              );
            })}
          </div>

          {/* Enhanced Mobile menu button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden p-3 rounded-xl text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 relative group"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 to-yellow-500/0 group-hover:from-orange-500/20 group-hover:to-yellow-500/20 rounded-xl transition-all duration-300"></div>
            {isOpen ? <X className="w-6 h-6 relative z-10" /> : <Menu className="w-6 h-6 relative z-10" />}
          </button>
        </div>
      </div>

      {/* Enhanced Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-black/20 backdrop-blur-2xl border-t border-orange-500/20"
          >
            <div className="px-4 py-4 space-y-2">
              {navItems.map((item, index) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                return (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      to={item.path}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 relative group ${
                        isActive
                          ? 'text-orange-400 bg-orange-500/10 border border-orange-500/30 shadow-lg shadow-orange-500/20'
                          : 'text-gray-300 hover:text-white hover:bg-white/5'
                      }`}
                    >
                      <Icon className={`w-5 h-5 transition-all duration-300 ${isActive ? 'text-orange-400' : 'group-hover:text-orange-400'}`} />
                      <span>{item.name}</span>
                      {/* Mobile hover glow */}
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 to-yellow-500/0 group-hover:from-orange-500/10 group-hover:to-yellow-500/10 rounded-xl transition-all duration-300"></div>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;